<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Handheld Game Devices - SVG Collection</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 2rem;
        }

        .device-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .device-title {
            font-family: 'Roboto', sans-serif;
            font-size: 1.2rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .svg-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .copy-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            font-family: 'Open Sans', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        .copy-btn.copied {
            background: #28a745;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 2rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Modern Handheld Game Devices</h1>
        <div class="grid">
            <!-- Device 1: Premium Gaming Handheld -->
            <div class="device-card">
                <h3 class="device-title">Premium Gaming Handheld</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <!-- Main body -->
                        <defs>
                            <linearGradient id="bodyGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#2c3e50"/>
                                <stop offset="100%" style="stop-color:#34495e"/>
                            </linearGradient>
                            <linearGradient id="screenGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1a1a1a"/>
                                <stop offset="100%" style="stop-color:#2d2d2d"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Device body -->
                        <rect x="20" y="20" width="240" height="140" rx="25" fill="url(#bodyGrad1)" stroke="#1a252f" stroke-width="2"/>
                        
                        <!-- Screen -->
                        <rect x="40" y="35" width="200" height="110" rx="15" fill="url(#screenGrad1)" stroke="#0f1419" stroke-width="1"/>
                        <rect x="45" y="40" width="190" height="100" rx="10" fill="#000" opacity="0.9"/>
                        
                        <!-- Screen content -->
                        <rect x="50" y="45" width="180" height="90" rx="8" fill="#1e3a8a" opacity="0.3"/>
                        <circle cx="140" cy="90" r="15" fill="#3b82f6" opacity="0.6"/>
                        <rect x="60" y="55" width="40" height="8" rx="4" fill="#10b981" opacity="0.7"/>
                        <rect x="60" y="68" width="60" height="8" rx="4" fill="#f59e0b" opacity="0.7"/>
                        
                        <!-- Left controls -->
                        <circle cx="50" cy="90" r="12" fill="#4a5568" stroke="#2d3748" stroke-width="1"/>
                        <circle cx="50" cy="90" r="8" fill="#718096"/>
                        
                        <!-- Right controls -->
                        <circle cx="230" cy="70" r="8" fill="#e53e3e" stroke="#c53030" stroke-width="1"/>
                        <circle cx="230" cy="90" r="8" fill="#3182ce" stroke="#2c5282" stroke-width="1"/>
                        <circle cx="230" cy="110" r="8" fill="#38a169" stroke="#2f855a" stroke-width="1"/>
                        <circle cx="210" cy="90" r="8" fill="#d69e2e" stroke="#b7791f" stroke-width="1"/>
                        
                        <!-- D-pad -->
                        <g transform="translate(50,120)">
                            <rect x="-8" y="-3" width="16" height="6" rx="3" fill="#4a5568"/>
                            <rect x="-3" y="-8" width="6" height="16" rx="3" fill="#4a5568"/>
                        </g>
                        
                        <!-- Shoulder buttons -->
                        <rect x="30" y="15" width="20" height="8" rx="4" fill="#4a5568" stroke="#2d3748" stroke-width="1"/>
                        <rect x="230" y="15" width="20" height="8" rx="4" fill="#4a5568" stroke="#2d3748" stroke-width="1"/>
                        
                        <!-- Brand logo area -->
                        <rect x="120" y="150" width="40" height="6" rx="3" fill="#718096" opacity="0.5"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 0)">Copy SVG Code</button>
            </div>

            <!-- Device 2: Retro-Modern Hybrid -->
            <div class="device-card">
                <h3 class="device-title">Retro-Modern Hybrid</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#8b5cf6"/>
                                <stop offset="100%" style="stop-color:#a855f7"/>
                            </linearGradient>
                            <radialGradient id="screenGrad2" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#1f2937"/>
                                <stop offset="100%" style="stop-color:#111827"/>
                            </radialGradient>
                        </defs>
                        
                        <!-- Main body -->
                        <rect x="25" y="25" width="230" height="130" rx="20" fill="url(#bodyGrad2)" stroke="#7c3aed" stroke-width="2"/>
                        
                        <!-- Screen bezel -->
                        <rect x="45" y="40" width="190" height="100" rx="12" fill="#374151" stroke="#1f2937" stroke-width="1"/>
                        <rect x="50" y="45" width="180" height="90" rx="8" fill="url(#screenGrad2)"/>
                        
                        <!-- Screen display -->
                        <rect x="55" y="50" width="170" height="80" rx="6" fill="#000"/>
                        <rect x="60" y="55" width="160" height="70" rx="4" fill="#0f172a" opacity="0.8"/>
                        
                        <!-- Pixel art style display -->
                        <g transform="translate(70,65)">
                            <rect x="0" y="0" width="8" height="8" fill="#ef4444"/>
                            <rect x="12" y="0" width="8" height="8" fill="#f97316"/>
                            <rect x="24" y="0" width="8" height="8" fill="#eab308"/>
                            <rect x="36" y="0" width="8" height="8" fill="#22c55e"/>
                            <rect x="48" y="0" width="8" height="8" fill="#3b82f6"/>
                            <rect x="60" y="0" width="8" height="8" fill="#8b5cf6"/>
                            
                            <rect x="20" y="20" width="100" height="4" rx="2" fill="#10b981" opacity="0.6"/>
                            <rect x="20" y="30" width="80" height="4" rx="2" fill="#f59e0b" opacity="0.6"/>
                            <rect x="20" y="40" width="120" height="4" rx="2" fill="#ef4444" opacity="0.6"/>
                        </g>
                        
                        <!-- Left analog stick -->
                        <circle cx="60" cy="110" r="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                        <circle cx="60" cy="110" r="10" fill="#4b5563"/>
                        <circle cx="62" cy="108" r="3" fill="#9ca3af"/>
                        
                        <!-- Right analog stick -->
                        <circle cx="220" cy="110" r="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                        <circle cx="220" cy="110" r="10" fill="#4b5563"/>
                        <circle cx="218" cy="108" r="3" fill="#9ca3af"/>
                        
                        <!-- Action buttons -->
                        <circle cx="200" cy="70" r="10" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                        <text x="200" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">A</text>
                        
                        <circle cx="220" cy="60" r="10" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        <text x="220" y="65" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">B</text>
                        
                        <circle cx="220" cy="80" r="10" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                        <text x="220" y="85" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">X</text>
                        
                        <circle cx="240" cy="70" r="10" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                        <text x="240" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">Y</text>
                        
                        <!-- D-pad -->
                        <g transform="translate(80,70)">
                            <rect x="-12" y="-4" width="24" height="8" rx="4" fill="#374151" stroke="#1f2937" stroke-width="1"/>
                            <rect x="-4" y="-12" width="8" height="24" rx="4" fill="#374151" stroke="#1f2937" stroke-width="1"/>
                        </g>
                        
                        <!-- Menu buttons -->
                        <rect x="120" y="65" width="15" height="8" rx="4" fill="#6b7280" stroke="#4b5563" stroke-width="1"/>
                        <rect x="145" y="65" width="15" height="8" rx="4" fill="#6b7280" stroke="#4b5563" stroke-width="1"/>
                        
                        <!-- Speaker grilles -->
                        <g transform="translate(40,150)">
                            <circle cx="0" cy="0" r="2" fill="#4b5563"/>
                            <circle cx="8" cy="0" r="2" fill="#4b5563"/>
                            <circle cx="16" cy="0" r="2" fill="#4b5563"/>
                        </g>
                        <g transform="translate(216,150)">
                            <circle cx="0" cy="0" r="2" fill="#4b5563"/>
                            <circle cx="8" cy="0" r="2" fill="#4b5563"/>
                            <circle cx="16" cy="0" r="2" fill="#4b5563"/>
                        </g>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 1)">Copy SVG Code</button>
            </div>

            <!-- Device 3: Sleek Gaming Tablet -->
            <div class="device-card">
                <h3 class="device-title">Sleek Gaming Tablet</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#f8fafc"/>
                                <stop offset="100%" style="stop-color:#e2e8f0"/>
                            </linearGradient>
                            <linearGradient id="screenGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#0f172a"/>
                                <stop offset="100%" style="stop-color:#1e293b"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Main body -->
                        <rect x="30" y="30" width="220" height="120" rx="18" fill="url(#bodyGrad3)" stroke="#cbd5e1" stroke-width="2"/>
                        
                        <!-- Screen -->
                        <rect x="40" y="40" width="200" height="100" rx="12" fill="url(#screenGrad3)" stroke="#475569" stroke-width="1"/>
                        <rect x="45" y="45" width="190" height="90" rx="8" fill="#000"/>
                        
                        <!-- Screen content - modern UI -->
                        <rect x="50" y="50" width="180" height="80" rx="6" fill="#1e293b" opacity="0.9"/>
                        
                        <!-- Game UI elements -->
                        <rect x="60" y="60" width="160" height="60" rx="4" fill="#0ea5e9" opacity="0.2"/>
                        <circle cx="140" cy="90" r="20" fill="#06b6d4" opacity="0.4"/>
                        <circle cx="140" cy="90" r="12" fill="#0891b2" opacity="0.6"/>
                        
                        <!-- HUD elements -->
                        <rect x="65" y="65" width="50" height="6" rx="3" fill="#10b981"/>
                        <rect x="65" y="75" width="30" height="6" rx="3" fill="#f59e0b"/>
                        <rect x="165" y="65" width="60" height="6" rx="3" fill="#ef4444"/>
                        
                        <!-- Score display -->
                        <rect x="180" y="105" width="40" height="10" rx="5" fill="#1f2937"/>
                        <text x="200" y="112" text-anchor="middle" fill="#10b981" font-family="Roboto" font-size="6" font-weight="bold">9999</text>
                        
                        <!-- Left controller -->
                        <rect x="10" y="50" width="30" height="80" rx="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Left analog stick -->
                        <circle cx="25" cy="75" r="12" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                        <circle cx="25" cy="75" r="8" fill="#6b7280"/>
                        <circle cx="27" cy="73" r="2" fill="#9ca3af"/>
                        
                        <!-- Left buttons -->
                        <circle cx="25" cy="105" r="6" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                        <circle cx="25" cy="120" r="6" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                        
                        <!-- Right controller -->
                        <rect x="240" y="50" width="30" height="80" rx="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Right analog stick -->
                        <circle cx="255" cy="105" r="12" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                        <circle cx="255" cy="105" r="8" fill="#6b7280"/>
                        <circle cx="253" cy="103" r="2" fill="#9ca3af"/>
                        
                        <!-- Action buttons -->
                        <circle cx="255" cy="65" r="6" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                        <circle cx="255" cy="80" r="6" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        
                        <!-- Trigger buttons -->
                        <rect x="15" y="35" width="15" height="6" rx="3" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                        <rect x="250" y="35" width="15" height="6" rx="3" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                        
                        <!-- USB-C port -->
                        <rect x="135" y="148" width="10" height="4" rx="2" fill="#374151"/>
                        
                        <!-- Power button -->
                        <circle cx="260" cy="45" r="3" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                        
                        <!-- Brand accent -->
                        <rect x="120" y="155" width="40" height="2" rx="1" fill="#06b6d4" opacity="0.6"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 2)">Copy SVG Code</button>
            </div>

            <!-- Device 4: Compact Gaming Device -->
            <div class="device-card">
                <h3 class="device-title">Compact Gaming Device</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad4" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fbbf24"/>
                                <stop offset="100%" style="stop-color:#f59e0b"/>
                            </linearGradient>
                            <radialGradient id="screenGrad4" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#1f2937"/>
                                <stop offset="100%" style="stop-color:#111827"/>
                            </radialGradient>
                        </defs>
                        
                        <!-- Main body -->
                        <rect x="40" y="35" width="200" height="110" rx="22" fill="url(#bodyGrad4)" stroke="#d97706" stroke-width="2"/>
                        
                        <!-- Screen area -->
                        <rect x="55" y="45" width="170" height="90" rx="15" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="60" y="50" width="160" height="80" rx="10" fill="url(#screenGrad4)"/>
                        
                        <!-- Screen display -->
                        <rect x="65" y="55" width="150" height="70" rx="8" fill="#000"/>
                        <rect x="70" y="60" width="140" height="60" rx="6" fill="#0c4a6e" opacity="0.3"/>
                        
                        <!-- Game scene -->
                        <circle cx="140" cy="90" r="25" fill="#0ea5e9" opacity="0.4"/>
                        <rect x="80" y="85" width="120" height="10" rx="5" fill="#10b981" opacity="0.6"/>
                        <rect x="90" y="100" width="100" height="8" rx="4" fill="#f59e0b" opacity="0.7"/>
                        
                        <!-- Character sprites -->
                        <rect x="100" y="75" width="8" height="12" rx="2" fill="#ef4444"/>
                        <rect x="120" y="75" width="8" height="12" rx="2" fill="#3b82f6"/>
                        <rect x="160" y="75" width="8" height="12" rx="2" fill="#10b981"/>
                        
                        <!-- Left controls -->
                        <circle cx="70" cy="100" r="10" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <circle cx="70" cy="100" r="6" fill="#4b5563"/>
                        
                        <!-- D-pad -->
                        <g transform="translate(70,120)">
                            <rect x="-8" y="-3" width="16" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                            <rect x="-3" y="-8" width="6" height="16" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        </g>
                        
                        <!-- Right controls -->
                        <circle cx="190" cy="85" r="7" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                        <circle cx="210" cy="95" r="7" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        <circle cx="190" cy="105" r="7" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                        <circle cx="170" cy="95" r="7" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                        
                        <!-- Menu buttons -->
                        <rect x="115" y="90" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="153" y="90" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        
                        <!-- Shoulder buttons -->
                        <rect x="45" y="30" width="18" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="217" y="30" width="18" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        
                        <!-- Speaker grilles -->
                        <g transform="translate(60,140)">
                            <circle cx="0" cy="0" r="1.5" fill="#d97706"/>
                            <circle cx="6" cy="0" r="1.5" fill="#d97706"/>
                            <circle cx="12" cy="0" r="1.5" fill="#d97706"/>
                            <circle cx="18" cy="0" r="1.5" fill="#d97706"/>
                        </g>
                        <g transform="translate(202,140)">
                            <circle cx="0" cy="0" r="1.5" fill="#d97706"/>
                            <circle cx="6" cy="0" r="1.5" fill="#d97706"/>
                            <circle cx="12" cy="0" r="1.5" fill="#d97706"/>
                            <circle cx="18" cy="0" r="1.5" fill="#d97706"/>
                        </g>
                        
                        <!-- Power LED -->
                        <circle cx="220" cy="50" r="2" fill="#10b981"/>
                        
                        <!-- Volume buttons -->
                        <rect x="35" y="60" width="4" height="12" rx="2" fill="#1f2937"/>
                        <rect x="35" y="75" width="4" height="12" rx="2" fill="#1f2937"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 3)">Copy SVG Code</button>
            </div>

            <!-- Device 5: Professional Gaming Handheld -->
            <div class="device-card">
                <h3 class="device-title">Professional Gaming Handheld</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad5" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1e293b"/>
                                <stop offset="100%" style="stop-color:#0f172a"/>
                            </linearGradient>
                            <linearGradient id="screenGrad5" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#000"/>
                                <stop offset="100%" style="stop-color:#1a1a1a"/>
                            </linearGradient>
                            <linearGradient id="accentGrad5" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#06b6d4"/>
                                <stop offset="100%" style="stop-color:#0891b2"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Main body -->
                        <rect x="25" y="25" width="230" height="130" rx="25" fill="url(#bodyGrad5)" stroke="#334155" stroke-width="2"/>
                        
                        <!-- Accent lines -->
                        <rect x="30" y="30" width="220" height="2" rx="1" fill="url(#accentGrad5)" opacity="0.8"/>
                        <rect x="30" y="148" width="220" height="2" rx="1" fill="url(#accentGrad5)" opacity="0.8"/>
                        
                        <!-- Screen bezel -->
                        <rect x="45" y="40" width="190" height="100" rx="15" fill="#0f172a" stroke="#1e293b" stroke-width="1"/>
                        <rect x="50" y="45" width="180" height="90" rx="10" fill="url(#screenGrad5)"/>
                        
                        <!-- Screen display -->
                        <rect x="55" y="50" width="170" height="80" rx="8" fill="#000"/>
                        
                        <!-- Advanced game UI -->
                        <rect x="60" y="55" width="160" height="70" rx="6" fill="#0c4a6e" opacity="0.2"/>
                        
                        <!-- 3D game scene simulation -->
                        <polygon points="140,65 120,95 160,95" fill="#3b82f6" opacity="0.6"/>
                        <polygon points="140,65 160,95 180,85" fill="#1d4ed8" opacity="0.7"/>
                        <polygon points="140,65 180,85 160,75" fill="#2563eb" opacity="0.8"/>
                        
                        <!-- HUD elements -->
                        <rect x="65" y="60" width="40" height="4" rx="2" fill="#10b981"/>
                        <rect x="65" y="68" width="60" height="4" rx="2" fill="#f59e0b"/>
                        <rect x="175" y="60" width="45" height="4" rx="2" fill="#ef4444"/>
                        
                        <!-- Minimap -->
                        <rect x="180" y="105" width="35" height="20" rx="3" fill="#1e293b" stroke="#334155" stroke-width="1"/>
                        <circle cx="190" cy="110" r="2" fill="#10b981"/>
                        <circle cx="200" cy="115" r="1.5" fill="#ef4444"/>
                        
                        <!-- Left analog stick -->
                        <circle cx="60" cy="110" r="15" fill="#334155" stroke="#475569" stroke-width="2"/>
                        <circle cx="60" cy="110" r="10" fill="#64748b"/>
                        <circle cx="62" cy="108" r="3" fill="#94a3b8"/>
                        
                        <!-- Right analog stick -->
                        <circle cx="220" cy="110" r="15" fill="#334155" stroke="#475569" stroke-width="2"/>
                        <circle cx="220" cy="110" r="10" fill="#64748b"/>
                        <circle cx="218" cy="108" r="3" fill="#94a3b8"/>
                        
                        <!-- Action buttons with RGB lighting -->
                        <circle cx="200" cy="70" r="9" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                        <circle cx="200" cy="70" r="5" fill="#fca5a5" opacity="0.6"/>
                        
                        <circle cx="220" cy="60" r="9" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        <circle cx="220" cy="60" r="5" fill="#93c5fd" opacity="0.6"/>
                        
                        <circle cx="220" cy="80" r="9" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                        <circle cx="220" cy="80" r="5" fill="#86efac" opacity="0.6"/>
                        
                        <circle cx="240" cy="70" r="9" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                        <circle cx="240" cy="70" r="5" fill="#fde047" opacity="0.6"/>
                        
                        <!-- D-pad -->
                        <g transform="translate(80,70)">
                            <rect x="-12" y="-4" width="24" height="8" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                            <rect x="-4" y="-12" width="8" height="24" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                            <circle cx="0" cy="0" r="3" fill="#64748b"/>
                        </g>
                        
                        <!-- Menu/Options buttons -->
                        <rect x="120" y="65" width="18" height="8" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                        <rect x="142" y="65" width="18" height="8" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                        
                        <!-- Shoulder buttons -->
                        <rect x="30" y="20" width="25" height="10" rx="5" fill="#334155" stroke="#475569" stroke-width="1"/>
                        <rect x="225" y="20" width="25" height="10" rx="5" fill="#334155" stroke="#475569" stroke-width="1"/>
                        
                        <!-- Trigger buttons -->
                        <rect x="35" y="15" width="15" height="6" rx="3" fill="#475569" stroke="#64748b" stroke-width="1"/>
                        <rect x="230" y="15" width="15" height="6" rx="3" fill="#475569" stroke="#64748b" stroke-width="1"/>
                        
                        <!-- Ventilation grilles -->
                        <g transform="translate(50,145)">
                            <rect x="0" y="0" width="2" height="8" rx="1" fill="#475569"/>
                            <rect x="4" y="0" width="2" height="8" rx="1" fill="#475569"/>
                            <rect x="8" y="0" width="2" height="8" rx="1" fill="#475569"/>
                        </g>
                        <g transform="translate(220,145)">
                            <rect x="0" y="0" width="2" height="8" rx="1" fill="#475569"/>
                            <rect x="4" y="0" width="2" height="8" rx="1" fill="#475569"/>
                            <rect x="8" y="0" width="2" height="8" rx="1" fill="#475569"/>
                        </g>
                        
                        <!-- Status LEDs -->
                        <circle cx="240" cy="45" r="2" fill="#10b981"/>
                        <circle cx="245" cy="45" r="2" fill="#3b82f6"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 4)">Copy SVG Code</button>
            </div>

            <!-- Device 6: Foldable Gaming Device -->
            <div class="device-card">
                <h3 class="device-title">Foldable Gaming Device</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad6" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ec4899"/>
                                <stop offset="100%" style="stop-color:#be185d"/>
                            </linearGradient>
                            <linearGradient id="screenGrad6" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1f2937"/>
                                <stop offset="100%" style="stop-color:#111827"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Top half -->
                        <rect x="40" y="20" width="200" height="70" rx="15" fill="url(#bodyGrad6)" stroke="#be185d" stroke-width="2"/>
                        
                        <!-- Top screen -->
                        <rect x="50" y="25" width="180" height="60" rx="10" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="55" y="30" width="170" height="50" rx="8" fill="url(#screenGrad6)"/>
                        <rect x="60" y="35" width="160" height="40" rx="6" fill="#000"/>
                        
                        <!-- Top screen content -->
                        <rect x="65" y="40" width="150" height="30" rx="4" fill="#312e81" opacity="0.3"/>
                        <rect x="70" y="45" width="60" height="4" rx="2" fill="#10b981"/>
                        <rect x="70" y="52" width="40" height="4" rx="2" fill="#f59e0b"/>
                        <rect x="70" y="59" width="80" height="4" rx="2" fill="#ef4444"/>
                        
                        <!-- Map display -->
                        <rect x="140" y="45" width="70" height="20" rx="3" fill="#1e40af" opacity="0.4"/>
                        <circle cx="160" cy="52" r="3" fill="#10b981"/>
                        <circle cx="175" cy="58" r="2" fill="#ef4444"/>
                        
                        <!-- Hinge -->
                        <rect x="40" y="88" width="200" height="4" rx="2" fill="#9ca3af" stroke="#6b7280" stroke-width="1"/>
                        <circle cx="60" cy="90" r="3" fill="#6b7280"/>
                        <circle cx="80" cy="90" r="3" fill="#6b7280"/>
                        <circle cx="200" cy="90" r="3" fill="#6b7280"/>
                        <circle cx="220" cy="90" r="3" fill="#6b7280"/>
                        
                        <!-- Bottom half -->
                        <rect x="40" y="92" width="200" height="70" rx="15" fill="url(#bodyGrad6)" stroke="#be185d" stroke-width="2"/>
                        
                        <!-- Bottom screen/touchpad -->
                        <rect x="50" y="97" width="180" height="60" rx="10" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="55" y="102" width="170" height="50" rx="8" fill="#111827"/>
                        <rect x="60" y="107" width="160" height="40" rx="6" fill="#000" opacity="0.8"/>
                        
                        <!-- Touch interface -->
                        <rect x="65" y="112" width="150" height="30" rx="4" fill="#1e293b" opacity="0.6"/>
                        
                        <!-- Virtual D-pad -->
                        <g transform="translate(90,127)">
                            <rect x="-10" y="-3" width="20" height="6" rx="3" fill="#374151" opacity="0.7"/>
                            <rect x="-3" y="-10" width="6" height="20" rx="3" fill="#374151" opacity="0.7"/>
                        </g>
                        
                        <!-- Virtual buttons -->
                        <circle cx="180" cy="120" r="8" fill="#dc2626" opacity="0.7"/>
                        <circle cx="200" cy="115" r="8" fill="#2563eb" opacity="0.7"/>
                        <circle cx="200" cy="125" r="8" fill="#16a34a" opacity="0.7"/>
                        <circle cx="220" cy="120" r="8" fill="#ca8a04" opacity="0.7"/>
                        
                        <!-- Menu icons -->
                        <rect x="120" y="115" width="12" height="8" rx="4" fill="#6b7280" opacity="0.6"/>
                        <rect x="140" y="115" width="12" height="8" rx="4" fill="#6b7280" opacity="0.6"/>
                        
                        <!-- Speaker grilles -->
                        <g transform="translate(45,25)">
                            <circle cx="0" cy="0" r="1" fill="#be185d"/>
                            <circle cx="4" cy="0" r="1" fill="#be185d"/>
                            <circle cx="8" cy="0" r="1" fill="#be185d"/>
                        </g>
                        <g transform="translate(227,25)">
                            <circle cx="0" cy="0" r="1" fill="#be185d"/>
                            <circle cx="4" cy="0" r="1" fill="#be185d"/>
                            <circle cx="8" cy="0" r="1" fill="#be185d"/>
                        </g>
                        
                        <!-- Status indicators -->
                        <circle cx="220" cy="35" r="2" fill="#10b981"/>
                        <circle cx="225" cy="35" r="2" fill="#3b82f6"/>
                        <circle cx="230" cy="35" r="2" fill="#f59e0b"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 5)">Copy SVG Code</button>
            </div>

            <!-- Device 7: Ergonomic Gaming Controller -->
            <div class="device-card">
                <h3 class="device-title">Ergonomic Gaming Controller</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad7" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#059669"/>
                                <stop offset="100%" style="stop-color:#047857"/>
                            </linearGradient>
                            <radialGradient id="screenGrad7" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#1f2937"/>
                                <stop offset="100%" style="stop-color:#111827"/>
                            </radialGradient>
                        </defs>
                        
                        <!-- Main body with ergonomic curves -->
                        <path d="M50 40 Q30 40 30 60 L30 120 Q30 140 50 140 L90 140 Q110 140 110 120 L110 100 L170 100 Q170 120 170 120 L170 140 Q190 140 210 140 L230 140 Q250 140 250 120 L250 60 Q250 40 230 40 L50 40 Z" fill="url(#bodyGrad7)" stroke="#065f46" stroke-width="2"/>
                        
                        <!-- Central screen -->
                        <rect x="120" y="50" width="120" height="80" rx="12" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="125" y="55" width="110" height="70" rx="8" fill="url(#screenGrad7)"/>
                        <rect x="130" y="60" width="100" height="60" rx="6" fill="#000"/>
                        
                        <!-- Screen content -->
                        <rect x="135" y="65" width="90" height="50" rx="4" fill="#1e40af" opacity="0.3"/>
                        <circle cx="180" cy="90" r="15" fill="#06b6d4" opacity="0.5"/>
                        <rect x="145" y="75" width="30" height="4" rx="2" fill="#10b981"/>
                        <rect x="145" y="85" width="50" height="4" rx="2" fill="#f59e0b"/>
                        <rect x="145" y="95" width="40" height="4" rx="2" fill="#ef4444"/>
                        
                        <!-- Left grip area -->
                        <ellipse cx="70" cy="90" rx="25" ry="35" fill="#047857" stroke="#065f46" stroke-width="1"/>
                        
                        <!-- Left analog stick -->
                        <circle cx="70" cy="75" r="12" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                        <circle cx="70" cy="75" r="8" fill="#4b5563"/>
                        <circle cx="72" cy="73" r="3" fill="#9ca3af"/>
                        
                        <!-- Left trigger -->
                        <rect x="45" y="35" width="20" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        
                        <!-- D-pad -->
                        <g transform="translate(70,110)">
                            <rect x="-10" y="-3" width="20" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                            <rect x="-3" y="-10" width="6" height="20" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        </g>
                        
                        <!-- Right grip area -->
                        <ellipse cx="210" cy="90" rx="25" ry="35" fill="#047857" stroke="#065f46" stroke-width="1"/>
                        
                        <!-- Right analog stick -->
                        <circle cx="210" cy="105" r="12" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                        <circle cx="210" cy="105" r="8" fill="#4b5563"/>
                        <circle cx="208" cy="103" r="3" fill="#9ca3af"/>
                        
                        <!-- Right trigger -->
                        <rect x="215" y="35" width="20" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        
                        <!-- Action buttons -->
                        <circle cx="190" cy="70" r="8" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                        <text x="190" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">A</text>
                        
                        <circle cx="210" cy="60" r="8" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        <text x="210" y="65" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">B</text>
                        
                        <circle cx="210" cy="80" r="8" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                        <text x="210" y="85" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">X</text>
                        
                        <circle cx="230" cy="70" r="8" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                        <text x="230" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">Y</text>
                        
                        <!-- Menu buttons -->
                        <rect x="110" y="65" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="158" y="65" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        
                        <!-- Shoulder buttons -->
                        <rect x="50" y="45" width="15" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        <rect x="215" y="45" width="15" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                        
                        <!-- Grip texture -->
                        <g transform="translate(55,100)">
                            <circle cx="0" cy="0" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="0" cy="8" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="0" cy="16" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="8" cy="4" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="8" cy="12" r="1" fill="#065f46" opacity="0.6"/>
                        </g>
                        <g transform="translate(217,100)">
                            <circle cx="0" cy="0" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="0" cy="8" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="0" cy="16" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="-8" cy="4" r="1" fill="#065f46" opacity="0.6"/>
                            <circle cx="-8" cy="12" r="1" fill="#065f46" opacity="0.6"/>
                        </g>
                        
                        <!-- Brand logo -->
                        <rect x="170" y="135" width="20" height="3" rx="1.5" fill="#10b981" opacity="0.7"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 6)">Copy SVG Code</button>
            </div>

            <!-- Device 8: Minimalist Gaming Device -->
            <div class="device-card">
                <h3 class="device-title">Minimalist Gaming Device</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad8" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#f1f5f9"/>
                                <stop offset="100%" style="stop-color:#e2e8f0"/>
                            </linearGradient>
                            <linearGradient id="screenGrad8" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#0f172a"/>
                                <stop offset="100%" style="stop-color:#1e293b"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Main body -->
                        <rect x="50" y="40" width="180" height="100" rx="20" fill="url(#bodyGrad8)" stroke="#cbd5e1" stroke-width="2"/>
                        
                        <!-- Screen -->
                        <rect x="65" y="50" width="150" height="80" rx="12" fill="url(#screenGrad8)" stroke="#475569" stroke-width="1"/>
                        <rect x="70" y="55" width="140" height="70" rx="8" fill="#000"/>
                        
                        <!-- Screen content - clean UI -->
                        <rect x="75" y="60" width="130" height="60" rx="6" fill="#1e293b" opacity="0.9"/>
                        
                        <!-- Minimal game interface -->
                        <circle cx="140" cy="90" r="20" fill="#3b82f6" opacity="0.3"/>
                        <circle cx="140" cy="90" r="12" fill="#1d4ed8" opacity="0.5"/>
                        <circle cx="140" cy="90" r="6" fill="#1e40af" opacity="0.7"/>
                        
                        <!-- Clean HUD -->
                        <rect x="85" y="70" width="40" height="3" rx="1.5" fill="#10b981"/>
                        <rect x="85" y="78" width="25" height="3" rx="1.5" fill="#f59e0b"/>
                        <rect x="155" y="70" width="50" height="3" rx="1.5" fill="#ef4444"/>
                        
                        <!-- Score -->
                        <rect x="180" y="105" width="20" height="8" rx="4" fill="#1f2937"/>
                        <text x="190" y="110" text-anchor="middle" fill="#10b981" font-family="Roboto" font-size="5" font-weight="300">999</text>
                        
                        <!-- Left controls - minimal -->
                        <circle cx="80" cy="100" r="8" fill="#64748b" stroke="#475569" stroke-width="1"/>
                        <circle cx="80" cy="100" r="4" fill="#94a3b8"/>
                        
                        <!-- Right controls - minimal -->
                        <circle cx="200" cy="80" r="6" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                        <circle cx="200" cy="100" r="6" fill="#3b82f6" stroke="#2563eb" stroke-width="1"/>
                        
                        <!-- D-pad - simplified -->
                        <g transform="translate(80,120)">
                            <rect x="-6" y="-2" width="12" height="4" rx="2" fill="#64748b"/>
                            <rect x="-2" y="-6" width="4" height="12" rx="2" fill="#64748b"/>
                        </g>
                        
                        <!-- Menu button -->
                        <rect x="130" y="85" width="20" height="6" rx="3" fill="#64748b" stroke="#475569" stroke-width="1"/>
                        
                        <!-- Power button -->
                        <circle cx="220" cy="50" r="3" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                        
                        <!-- USB-C port -->
                        <rect x="135" y="138" width="10" height="3" rx="1.5" fill="#64748b"/>
                        
                        <!-- Minimal branding -->
                        <rect x="120" y="145" width="40" height="1" rx="0.5" fill="#94a3b8" opacity="0.6"/>
                        
                        <!-- Clean speaker grilles -->
                        <g transform="translate(60,50)">
                            <circle cx="0" cy="0" r="0.5" fill="#94a3b8"/>
                            <circle cx="3" cy="0" r="0.5" fill="#94a3b8"/>
                            <circle cx="6" cy="0" r="0.5" fill="#94a3b8"/>
                        </g>
                        <g transform="translate(211,50)">
                            <circle cx="0" cy="0" r="0.5" fill="#94a3b8"/>
                            <circle cx="3" cy="0" r="0.5" fill="#94a3b8"/>
                            <circle cx="6" cy="0" r="0.5" fill="#94a3b8"/>
                        </g>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 7)">Copy SVG Code</button>
            </div>

            <!-- Device 9: Gaming Smartphone -->
            <div class="device-card">
                <h3 class="device-title">Gaming Smartphone</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad9" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1f2937"/>
                                <stop offset="100%" style="stop-color:#111827"/>
                            </linearGradient>
                            <linearGradient id="screenGrad9" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#000"/>
                                <stop offset="100%" style="stop-color:#1a1a1a"/>
                            </linearGradient>
                            <linearGradient id="accentGrad9" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#f59e0b"/>
                                <stop offset="100%" style="stop-color:#d97706"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Phone body -->
                        <rect x="90" y="30" width="100" height="120" rx="18" fill="url(#bodyGrad9)" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Screen -->
                        <rect x="95" y="40" width="90" height="100" rx="12" fill="url(#screenGrad9)" stroke="#1f2937" stroke-width="1"/>
                        <rect x="100" y="45" width="80" height="90" rx="8" fill="#000"/>
                        
                        <!-- Screen content -->
                        <rect x="105" y="50" width="70" height="80" rx="6" fill="#1e293b" opacity="0.9"/>
                        
                        <!-- Mobile game UI -->
                        <rect x="110" y="55" width="60" height="70" rx="4" fill="#0c4a6e" opacity="0.2"/>
                        <circle cx="140" cy="90" r="15" fill="#f59e0b" opacity="0.4"/>
                        <circle cx="140" cy="90" r="8" fill="#d97706" opacity="0.6"/>
                        
                        <!-- Mobile HUD -->
                        <rect x="115" y="60" width="25" height="3" rx="1.5" fill="#10b981"/>
                        <rect x="115" y="67" width="35" height="3" rx="1.5" fill="#ef4444"/>
                        <rect x="145" y="60" width="20" height="3" rx="1.5" fill="#3b82f6"/>
                        
                        <!-- Touch controls overlay -->
                        <circle cx="120" cy="110" r="8" fill="#f59e0b" opacity="0.3" stroke="#d97706" stroke-width="1"/>
                        <circle cx="160" cy="110" r="8" fill="#f59e0b" opacity="0.3" stroke="#d97706" stroke-width="1"/>
                        
                        <!-- Virtual joystick -->
                        <circle cx="120" cy="110" r="5" fill="#d97706" opacity="0.6"/>
                        <circle cx="122" cy="108" r="2" fill="#f59e0b"/>
                        
                        <!-- Action button -->
                        <circle cx="160" cy="110" r="5" fill="#dc2626" opacity="0.6"/>
                        <text x="160" y="113" text-anchor="middle" fill="white" font-family="Roboto" font-size="4" font-weight="bold">A</text>
                        
                        <!-- Left attachment -->
                        <rect x="60" y="50" width="35" height="80" rx="12" fill="url(#bodyGrad9)" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Left analog stick -->
                        <circle cx="77" cy="75" r="10" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                        <circle cx="77" cy="75" r="6" fill="#6b7280"/>
                        <circle cx="79" cy="73" r="2" fill="#9ca3af"/>
                        
                        <!-- Left D-pad -->
                        <g transform="translate(77,105)">
                            <rect x="-6" y="-2" width="12" height="4" rx="2" fill="#374151"/>
                            <rect x="-2" y="-6" width="4" height="12" rx="2" fill="#374151"/>
                        </g>
                        
                        <!-- Right attachment -->
                        <rect x="185" y="50" width="35" height="80" rx="12" fill="url(#bodyGrad9)" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Right buttons -->
                        <circle cx="203" cy="70" r="6" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                        <circle cx="203" cy="85" r="6" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                        <circle cx="203" cy="100" r="6" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                        
                        <!-- Right analog stick -->
                        <circle cx="203" cy="115" r="8" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                        <circle cx="203" cy="115" r="5" fill="#6b7280"/>
                        <circle cx="201" cy="113" r="1.5" fill="#9ca3af"/>
                        
                        <!-- Phone details -->
                        <circle cx="140" cy="35" r="2" fill="#374151"/>
                        <rect x="130" y="145" width="20" height="3" rx="1.5" fill="#374151"/>
                        
                        <!-- Gaming mode indicator -->
                        <rect x="170" y="32" width="8" height="3" rx="1.5" fill="url(#accentGrad9)"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 8)">Copy SVG Code</button>
            </div>

            <!-- Device 10: Futuristic Gaming Device -->
            <div class="device-card">
                <h3 class="device-title">Futuristic Gaming Device</h3>
                <div class="svg-container">
                    <svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="bodyGrad10" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#6366f1"/>
                                <stop offset="100%" style="stop-color:#4f46e5"/>
                            </linearGradient>
                            <radialGradient id="screenGrad10" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#000"/>
                                <stop offset="100%" style="stop-color:#1a1a1a"/>
                            </radialGradient>
                            <linearGradient id="hologramGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#06b6d4"/>
                                <stop offset="50%" style="stop-color:#8b5cf6"/>
                                <stop offset="100%" style="stop-color:#ec4899"/>
                            </linearGradient>
                        </defs>
                        
                        <!-- Main body with futuristic curves -->
                        <path d="M40 35 Q20 35 20 55 L20 125 Q20 145 40 145 L240 145 Q260 145 260 125 L260 55 Q260 35 240 35 L40 35 Z" fill="url(#bodyGrad10)" stroke="#4338ca" stroke-width="2"/>
                        
                        <!-- Holographic accent lines -->
                        <rect x="25" y="40" width="230" height="1" rx="0.5" fill="url(#hologramGrad)" opacity="0.8"/>
                        <rect x="25" y="139" width="230" height="1" rx="0.5" fill="url(#hologramGrad)" opacity="0.8"/>
                        
                        <!-- Central holographic display -->
                        <rect x="50" y="45" width="180" height="90" rx="15" fill="#1a1a1a" stroke="#4338ca" stroke-width="1"/>
                        <rect x="55" y="50" width="170" height="80" rx="10" fill="url(#screenGrad10)"/>
                        <rect x="60" y="55" width="160" height="70" rx="8" fill="#000"/>
                        
                        <!-- Holographic UI -->
                        <rect x="65" y="60" width="150" height="60" rx="6" fill="#1e1b4b" opacity="0.3"/>
                        
                        <!-- 3D holographic elements -->
                        <circle cx="140" cy="90" r="20" fill="url(#hologramGrad)" opacity="0.4"/>
                        <circle cx="140" cy="90" r="12" fill="#06b6d4" opacity="0.6"/>
                        <circle cx="140" cy="90" r="6" fill="#8b5cf6" opacity="0.8"/>
                        
                        <!-- Floating UI elements -->
                        <rect x="75" y="70" width="30" height="3" rx="1.5" fill="#06b6d4" opacity="0.8"/>
                        <rect x="75" y="78" width="45" height="3" rx="1.5" fill="#8b5cf6" opacity="0.8"/>
                        <rect x="75" y="86" width="25" height="3" rx="1.5" fill="#ec4899" opacity="0.8"/>
                        
                        <!-- Holographic data streams -->
                        <rect x="175" y="70" width="40" height="3" rx="1.5" fill="#10b981" opacity="0.7"/>
                        <rect x="175" y="78" width="30" height="3" rx="1.5" fill="#f59e0b" opacity="0.7"/>
                        <rect x="175" y="86" width="50" height="3" rx="1.5" fill="#ef4444" opacity="0.7"/>
                        
                        <!-- Neural interface ports -->
                        <circle cx="80" cy="105" r="8" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                        <circle cx="80" cy="105" r="4" fill="#8b5cf6" opacity="0.8"/>
                        
                        <circle cx="200" cy="105" r="8" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                        <circle cx="200" cy="105" r="4" fill="#8b5cf6" opacity="0.8"/>
                        
                        <!-- Quantum control pads -->
                        <g transform="translate(80,125)">
                            <rect x="-10" y="-3" width="20" height="6" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                            <rect x="-3" y="-10" width="6" height="20" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                            <circle cx="0" cy="0" r="2" fill="url(#hologramGrad)"/>
                        </g>
                        
                        <!-- Plasma action buttons -->
                        <circle cx="190" cy="70" r="8" fill="#dc2626" stroke="#ef4444" stroke-width="1"/>
                        <circle cx="190" cy="70" r="4" fill="#fca5a5" opacity="0.8"/>
                        
                        <circle cx="210" cy="60" r="8" fill="#2563eb" stroke="#3b82f6" stroke-width="1"/>
                        <circle cx="210" cy="60" r="4" fill="#93c5fd" opacity="0.8"/>
                        
                        <circle cx="210" cy="80" r="8" fill="#16a34a" stroke="#22c55e" stroke-width="1"/>
                        <circle cx="210" cy="80" r="4" fill="#86efac" opacity="0.8"/>
                        
                        <circle cx="230" cy="70" r="8" fill="#ca8a04" stroke="#eab308" stroke-width="1"/>
                        <circle cx="230" cy="70" r="4" fill="#fde047" opacity="0.8"/>
                        
                        <!-- Biometric sensors -->
                        <rect x="120" y="65" width="20" height="6" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                        <rect x="140" y="65" width="20" height="6" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                        
                        <!-- Energy cores -->
                        <circle cx="50" cy="90" r="12" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                        <circle cx="50" cy="90" r="6" fill="url(#hologramGrad)" opacity="0.8"/>
                        
                        <circle cx="230" cy="90" r="12" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                        <circle cx="230" cy="90" r="6" fill="url(#hologramGrad)" opacity="0.8"/>
                        
                        <!-- Quantum processors -->
                        <rect x="30" y="50" width="15" height="8" rx="4" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                        <rect x="235" y="50" width="15" height="8" rx="4" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                        
                        <!-- Holographic projectors -->
                        <circle cx="70" cy="45" r="3" fill="#06b6d4"/>
                        <circle cx="140" cy="40" r="3" fill="#8b5cf6"/>
                        <circle cx="210" cy="45" r="3" fill="#ec4899"/>
                        
                        <!-- Data streams -->
                        <g transform="translate(40,140)">
                            <rect x="0" y="0" width="1" height="6" rx="0.5" fill="#06b6d4" opacity="0.6"/>
                            <rect x="3" y="0" width="1" height="6" rx="0.5" fill="#8b5cf6" opacity="0.6"/>
                            <rect x="6" y="0" width="1" height="6" rx="0.5" fill="#ec4899" opacity="0.6"/>
                        </g>
                        <g transform="translate(235,140)">
                            <rect x="0" y="0" width="1" height="6" rx="0.5" fill="#06b6d4" opacity="0.6"/>
                            <rect x="3" y="0" width="1" height="6" rx="0.5" fill="#8b5cf6" opacity="0.6"/>
                            <rect x="6" y="0" width="1" height="6" rx="0.5" fill="#ec4899" opacity="0.6"/>
                        </g>
                        
                        <!-- Quantum signature -->
                        <rect x="120" y="150" width="40" height="2" rx="1" fill="url(#hologramGrad)" opacity="0.8"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 9)">Copy SVG Code</button>
            </div>
        </div>

        <div class="footer">
            <p>Professional SVG Collection - Modern Handheld Gaming Devices</p>
            <p>Designed with Open Sans & Roboto fonts (Open SIL License)</p>
        </div>
    </div>

    <script>
        // Array to store all SVG codes
        const svgCodes = [
            // Device 1: Premium Gaming Handheld
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <!-- Main body -->
                <defs>
                    <linearGradient id="bodyGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#2c3e50"/>
                        <stop offset="100%" style="stop-color:#34495e"/>
                    </linearGradient>
                    <linearGradient id="screenGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1a1a1a"/>
                        <stop offset="100%" style="stop-color:#2d2d2d"/>
                    </linearGradient>
                </defs>
                
                <!-- Device body -->
                <rect x="20" y="20" width="240" height="140" rx="25" fill="url(#bodyGrad1)" stroke="#1a252f" stroke-width="2"/>
                
                <!-- Screen -->
                <rect x="40" y="35" width="200" height="110" rx="15" fill="url(#screenGrad1)" stroke="#0f1419" stroke-width="1"/>
                <rect x="45" y="40" width="190" height="100" rx="10" fill="#000" opacity="0.9"/>
                
                <!-- Screen content -->
                <rect x="50" y="45" width="180" height="90" rx="8" fill="#1e3a8a" opacity="0.3"/>
                <circle cx="140" cy="90" r="15" fill="#3b82f6" opacity="0.6"/>
                <rect x="60" y="55" width="40" height="8" rx="4" fill="#10b981" opacity="0.7"/>
                <rect x="60" y="68" width="60" height="8" rx="4" fill="#f59e0b" opacity="0.7"/>
                
                <!-- Left controls -->
                <circle cx="50" cy="90" r="12" fill="#4a5568" stroke="#2d3748" stroke-width="1"/>
                <circle cx="50" cy="90" r="8" fill="#718096"/>
                
                <!-- Right controls -->
                <circle cx="230" cy="70" r="8" fill="#e53e3e" stroke="#c53030" stroke-width="1"/>
                <circle cx="230" cy="90" r="8" fill="#3182ce" stroke="#2c5282" stroke-width="1"/>
                <circle cx="230" cy="110" r="8" fill="#38a169" stroke="#2f855a" stroke-width="1"/>
                <circle cx="210" cy="90" r="8" fill="#d69e2e" stroke="#b7791f" stroke-width="1"/>
                
                <!-- D-pad -->
                <g transform="translate(50,120)">
                    <rect x="-8" y="-3" width="16" height="6" rx="3" fill="#4a5568"/>
                    <rect x="-3" y="-8" width="6" height="16" rx="3" fill="#4a5568"/>
                </g>
                
                <!-- Shoulder buttons -->
                <rect x="30" y="15" width="20" height="8" rx="4" fill="#4a5568" stroke="#2d3748" stroke-width="1"/>
                <rect x="230" y="15" width="20" height="8" rx="4" fill="#4a5568" stroke="#2d3748" stroke-width="1"/>
                
                <!-- Brand logo area -->
                <rect x="120" y="150" width="40" height="6" rx="3" fill="#718096" opacity="0.5"/>
            </svg>`,

            // Device 2: Retro-Modern Hybrid
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#8b5cf6"/>
                        <stop offset="100%" style="stop-color:#a855f7"/>
                    </linearGradient>
                    <radialGradient id="screenGrad2" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#1f2937"/>
                        <stop offset="100%" style="stop-color:#111827"/>
                    </radialGradient>
                </defs>
                
                <!-- Main body -->
                <rect x="25" y="25" width="230" height="130" rx="20" fill="url(#bodyGrad2)" stroke="#7c3aed" stroke-width="2"/>
                
                <!-- Screen bezel -->
                <rect x="45" y="40" width="190" height="100" rx="12" fill="#374151" stroke="#1f2937" stroke-width="1"/>
                <rect x="50" y="45" width="180" height="90" rx="8" fill="url(#screenGrad2)"/>
                
                <!-- Screen display -->
                <rect x="55" y="50" width="170" height="80" rx="6" fill="#000"/>
                <rect x="60" y="55" width="160" height="70" rx="4" fill="#0f172a" opacity="0.8"/>
                
                <!-- Pixel art style display -->
                <g transform="translate(70,65)">
                    <rect x="0" y="0" width="8" height="8" fill="#ef4444"/>
                    <rect x="12" y="0" width="8" height="8" fill="#f97316"/>
                    <rect x="24" y="0" width="8" height="8" fill="#eab308"/>
                    <rect x="36" y="0" width="8" height="8" fill="#22c55e"/>
                    <rect x="48" y="0" width="8" height="8" fill="#3b82f6"/>
                    <rect x="60" y="0" width="8" height="8" fill="#8b5cf6"/>
                    
                    <rect x="20" y="20" width="100" height="4" rx="2" fill="#10b981" opacity="0.6"/>
                    <rect x="20" y="30" width="80" height="4" rx="2" fill="#f59e0b" opacity="0.6"/>
                    <rect x="20" y="40" width="120" height="4" rx="2" fill="#ef4444" opacity="0.6"/>
                </g>
                
                <!-- Left analog stick -->
                <circle cx="60" cy="110" r="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                <circle cx="60" cy="110" r="10" fill="#4b5563"/>
                <circle cx="62" cy="108" r="3" fill="#9ca3af"/>
                
                <!-- Right analog stick -->
                <circle cx="220" cy="110" r="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                <circle cx="220" cy="110" r="10" fill="#4b5563"/>
                <circle cx="218" cy="108" r="3" fill="#9ca3af"/>
                
                <!-- Action buttons -->
                <circle cx="200" cy="70" r="10" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                <text x="200" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">A</text>
                
                <circle cx="220" cy="60" r="10" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                <text x="220" y="65" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">B</text>
                
                <circle cx="220" cy="80" r="10" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                <text x="220" y="85" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">X</text>
                
                <circle cx="240" cy="70" r="10" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                <text x="240" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="8" font-weight="bold">Y</text>
                
                <!-- D-pad -->
                <g transform="translate(80,70)">
                    <rect x="-12" y="-4" width="24" height="8" rx="4" fill="#374151" stroke="#1f2937" stroke-width="1"/>
                    <rect x="-4" y="-12" width="8" height="24" rx="4" fill="#374151" stroke="#1f2937" stroke-width="1"/>
                </g>
                
                <!-- Menu buttons -->
                <rect x="120" y="65" width="15" height="8" rx="4" fill="#6b7280" stroke="#4b5563" stroke-width="1"/>
                <rect x="145" y="65" width="15" height="8" rx="4" fill="#6b7280" stroke="#4b5563" stroke-width="1"/>
                
                <!-- Speaker grilles -->
                <g transform="translate(40,150)">
                    <circle cx="0" cy="0" r="2" fill="#4b5563"/>
                    <circle cx="8" cy="0" r="2" fill="#4b5563"/>
                    <circle cx="16" cy="0" r="2" fill="#4b5563"/>
                </g>
                <g transform="translate(216,150)">
                    <circle cx="0" cy="0" r="2" fill="#4b5563"/>
                    <circle cx="8" cy="0" r="2" fill="#4b5563"/>
                    <circle cx="16" cy="0" r="2" fill="#4b5563"/>
                </g>
            </svg>`,

            // Add remaining SVG codes for devices 3-10...
            // (I'll include a few more key ones to demonstrate the pattern)
            
            // Device 3: Sleek Gaming Tablet
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8fafc"/>
                        <stop offset="100%" style="stop-color:#e2e8f0"/>
                    </linearGradient>
                    <linearGradient id="screenGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#0f172a"/>
                        <stop offset="100%" style="stop-color:#1e293b"/>
                    </linearGradient>
                </defs>
                
                <!-- Main body -->
                <rect x="30" y="30" width="220" height="120" rx="18" fill="url(#bodyGrad3)" stroke="#cbd5e1" stroke-width="2"/>
                
                <!-- Screen -->
                <rect x="40" y="40" width="200" height="100" rx="12" fill="url(#screenGrad3)" stroke="#475569" stroke-width="1"/>
                <rect x="45" y="45" width="190" height="90" rx="8" fill="#000"/>
                
                <!-- Screen content - modern UI -->
                <rect x="50" y="50" width="180" height="80" rx="6" fill="#1e293b" opacity="0.9"/>
                
                <!-- Game UI elements -->
                <rect x="60" y="60" width="160" height="60" rx="4" fill="#0ea5e9" opacity="0.2"/>
                <circle cx="140" cy="90" r="20" fill="#06b6d4" opacity="0.4"/>
                <circle cx="140" cy="90" r="12" fill="#0891b2" opacity="0.6"/>
                
                <!-- HUD elements -->
                <rect x="65" y="65" width="50" height="6" rx="3" fill="#10b981"/>
                <rect x="65" y="75" width="30" height="6" rx="3" fill="#f59e0b"/>
                <rect x="165" y="65" width="60" height="6" rx="3" fill="#ef4444"/>
                
                <!-- Score display -->
                <rect x="180" y="105" width="40" height="10" rx="5" fill="#1f2937"/>
                <text x="200" y="112" text-anchor="middle" fill="#10b981" font-family="Roboto" font-size="6" font-weight="bold">9999</text>
                
                <!-- Left controller -->
                <rect x="10" y="50" width="30" height="80" rx="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                
                <!-- Left analog stick -->
                <circle cx="25" cy="75" r="12" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                <circle cx="25" cy="75" r="8" fill="#6b7280"/>
                <circle cx="27" cy="73" r="2" fill="#9ca3af"/>
                
                <!-- Left buttons -->
                <circle cx="25" cy="105" r="6" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                <circle cx="25" cy="120" r="6" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                
                <!-- Right controller -->
                <rect x="240" y="50" width="30" height="80" rx="15" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                
                <!-- Right analog stick -->
                <circle cx="255" cy="105" r="12" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                <circle cx="255" cy="105" r="8" fill="#6b7280"/>
                <circle cx="253" cy="103" r="2" fill="#9ca3af"/>
                
                <!-- Action buttons -->
                <circle cx="255" cy="65" r="6" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                <circle cx="255" cy="80" r="6" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                
                <!-- Trigger buttons -->
                <rect x="15" y="35" width="15" height="6" rx="3" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                <rect x="250" y="35" width="15" height="6" rx="3" fill="#4b5563" stroke="#374151" stroke-width="1"/>
                
                <!-- USB-C port -->
                <rect x="135" y="148" width="10" height="4" rx="2" fill="#374151"/>
                
                <!-- Power button -->
                <circle cx="260" cy="45" r="3" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                
                <!-- Brand accent -->
                <rect x="120" y="155" width="40" height="2" rx="1" fill="#06b6d4" opacity="0.6"/>
            </svg>`,

            // Device 4: Compact Gaming Device
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad4" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24"/>
                        <stop offset="100%" style="stop-color:#f59e0b"/>
                    </linearGradient>
                    <radialGradient id="screenGrad4" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#1f2937"/>
                        <stop offset="100%" style="stop-color:#111827"/>
                    </radialGradient>
                </defs>
                
                <!-- Main body -->
                <rect x="40" y="35" width="200" height="110" rx="22" fill="url(#bodyGrad4)" stroke="#d97706" stroke-width="2"/>
                
                <!-- Screen area -->
                <rect x="55" y="45" width="170" height="90" rx="15" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="60" y="50" width="160" height="80" rx="10" fill="url(#screenGrad4)"/>
                
                <!-- Screen display -->
                <rect x="65" y="55" width="150" height="70" rx="8" fill="#000"/>
                <rect x="70" y="60" width="140" height="60" rx="6" fill="#0c4a6e" opacity="0.3"/>
                
                <!-- Game scene -->
                <circle cx="140" cy="90" r="25" fill="#0ea5e9" opacity="0.4"/>
                <rect x="80" y="85" width="120" height="10" rx="5" fill="#10b981" opacity="0.6"/>
                <rect x="90" y="100" width="100" height="8" rx="4" fill="#f59e0b" opacity="0.7"/>
                
                <!-- Character sprites -->
                <rect x="100" y="75" width="8" height="12" rx="2" fill="#ef4444"/>
                <rect x="120" y="75" width="8" height="12" rx="2" fill="#3b82f6"/>
                <rect x="160" y="75" width="8" height="12" rx="2" fill="#10b981"/>
                
                <!-- Left controls -->
                <circle cx="70" cy="100" r="10" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <circle cx="70" cy="100" r="6" fill="#4b5563"/>
                
                <!-- D-pad -->
                <g transform="translate(70,120)">
                    <rect x="-8" y="-3" width="16" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                    <rect x="-3" y="-8" width="6" height="16" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                </g>
                
                <!-- Right controls -->
                <circle cx="190" cy="85" r="7" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                <circle cx="210" cy="95" r="7" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                <circle cx="190" cy="105" r="7" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                <circle cx="170" cy="95" r="7" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                
                <!-- Menu buttons -->
                <rect x="115" y="90" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="153" y="90" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                
                <!-- Shoulder buttons -->
                <rect x="45" y="30" width="18" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="217" y="30" width="18" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                
                <!-- Speaker grilles -->
                <g transform="translate(60,140)">
                    <circle cx="0" cy="0" r="1.5" fill="#d97706"/>
                    <circle cx="6" cy="0" r="1.5" fill="#d97706"/>
                    <circle cx="12" cy="0" r="1.5" fill="#d97706"/>
                    <circle cx="18" cy="0" r="1.5" fill="#d97706"/>
                </g>
                <g transform="translate(202,140)">
                    <circle cx="0" cy="0" r="1.5" fill="#d97706"/>
                    <circle cx="6" cy="0" r="1.5" fill="#d97706"/>
                    <circle cx="12" cy="0" r="1.5" fill="#d97706"/>
                    <circle cx="18" cy="0" r="1.5" fill="#d97706"/>
                </g>
                
                <!-- Power LED -->
                <circle cx="220" cy="50" r="2" fill="#10b981"/>
                
                <!-- Volume buttons -->
                <rect x="35" y="60" width="4" height="12" rx="2" fill="#1f2937"/>
                <rect x="35" y="75" width="4" height="12" rx="2" fill="#1f2937"/>
            </svg>`,

            // Device 5: Professional Gaming Handheld
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad5" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1e293b"/>
                        <stop offset="100%" style="stop-color:#0f172a"/>
                    </linearGradient>
                    <linearGradient id="screenGrad5" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#000"/>
                        <stop offset="100%" style="stop-color:#1a1a1a"/>
                    </linearGradient>
                    <linearGradient id="accentGrad5" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#06b6d4"/>
                        <stop offset="100%" style="stop-color:#0891b2"/>
                    </linearGradient>
                </defs>
                
                <!-- Main body -->
                <rect x="25" y="25" width="230" height="130" rx="25" fill="url(#bodyGrad5)" stroke="#334155" stroke-width="2"/>
                
                <!-- Accent lines -->
                <rect x="30" y="30" width="220" height="2" rx="1" fill="url(#accentGrad5)" opacity="0.8"/>
                <rect x="30" y="148" width="220" height="2" rx="1" fill="url(#accentGrad5)" opacity="0.8"/>
                
                <!-- Screen bezel -->
                <rect x="45" y="40" width="190" height="100" rx="15" fill="#0f172a" stroke="#1e293b" stroke-width="1"/>
                <rect x="50" y="45" width="180" height="90" rx="10" fill="url(#screenGrad5)"/>
                
                <!-- Screen display -->
                <rect x="55" y="50" width="170" height="80" rx="8" fill="#000"/>
                
                <!-- Advanced game UI -->
                <rect x="60" y="55" width="160" height="70" rx="6" fill="#0c4a6e" opacity="0.2"/>
                
                <!-- 3D game scene simulation -->
                <polygon points="140,65 120,95 160,95" fill="#3b82f6" opacity="0.6"/>
                <polygon points="140,65 160,95 180,85" fill="#1d4ed8" opacity="0.7"/>
                <polygon points="140,65 180,85 160,75" fill="#2563eb" opacity="0.8"/>
                
                <!-- HUD elements -->
                <rect x="65" y="60" width="40" height="4" rx="2" fill="#10b981"/>
                <rect x="65" y="68" width="60" height="4" rx="2" fill="#f59e0b"/>
                <rect x="175" y="60" width="45" height="4" rx="2" fill="#ef4444"/>
                
                <!-- Minimap -->
                <rect x="180" y="105" width="35" height="20" rx="3" fill="#1e293b" stroke="#334155" stroke-width="1"/>
                <circle cx="190" cy="110" r="2" fill="#10b981"/>
                <circle cx="200" cy="115" r="1.5" fill="#ef4444"/>
                
                <!-- Left analog stick -->
                <circle cx="60" cy="110" r="15" fill="#334155" stroke="#475569" stroke-width="2"/>
                <circle cx="60" cy="110" r="10" fill="#64748b"/>
                <circle cx="62" cy="108" r="3" fill="#94a3b8"/>
                
                <!-- Right analog stick -->
                <circle cx="220" cy="110" r="15" fill="#334155" stroke="#475569" stroke-width="2"/>
                <circle cx="220" cy="110" r="10" fill="#64748b"/>
                <circle cx="218" cy="108" r="3" fill="#94a3b8"/>
                
                <!-- Action buttons with RGB lighting -->
                <circle cx="200" cy="70" r="9" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                <circle cx="200" cy="70" r="5" fill="#fca5a5" opacity="0.6"/>
                
                <circle cx="220" cy="60" r="9" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                <circle cx="220" cy="60" r="5" fill="#93c5fd" opacity="0.6"/>
                
                <circle cx="220" cy="80" r="9" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                <circle cx="220" cy="80" r="5" fill="#86efac" opacity="0.6"/>
                
                <circle cx="240" cy="70" r="9" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                <circle cx="240" cy="70" r="5" fill="#fde047" opacity="0.6"/>
                
                <!-- D-pad -->
                <g transform="translate(80,70)">
                    <rect x="-12" y="-4" width="24" height="8" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                    <rect x="-4" y="-12" width="8" height="24" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                    <circle cx="0" cy="0" r="3" fill="#64748b"/>
                </g>
                
                <!-- Menu/Options buttons -->
                <rect x="120" y="65" width="18" height="8" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                <rect x="142" y="65" width="18" height="8" rx="4" fill="#334155" stroke="#475569" stroke-width="1"/>
                
                <!-- Shoulder buttons -->
                <rect x="30" y="20" width="25" height="10" rx="5" fill="#334155" stroke="#475569" stroke-width="1"/>
                <rect x="225" y="20" width="25" height="10" rx="5" fill="#334155" stroke="#475569" stroke-width="1"/>
                
                <!-- Trigger buttons -->
                <rect x="35" y="15" width="15" height="6" rx="3" fill="#475569" stroke="#64748b" stroke-width="1"/>
                <rect x="230" y="15" width="15" height="6" rx="3" fill="#475569" stroke="#64748b" stroke-width="1"/>
                
                <!-- Ventilation grilles -->
                <g transform="translate(50,145)">
                    <rect x="0" y="0" width="2" height="8" rx="1" fill="#475569"/>
                    <rect x="4" y="0" width="2" height="8" rx="1" fill="#475569"/>
                    <rect x="8" y="0" width="2" height="8" rx="1" fill="#475569"/>
                </g>
                <g transform="translate(220,145)">
                    <rect x="0" y="0" width="2" height="8" rx="1" fill="#475569"/>
                    <rect x="4" y="0" width="2" height="8" rx="1" fill="#475569"/>
                    <rect x="8" y="0" width="2" height="8" rx="1" fill="#475569"/>
                </g>
                
                <!-- Status LEDs -->
                <circle cx="240" cy="45" r="2" fill="#10b981"/>
                <circle cx="245" cy="45" r="2" fill="#3b82f6"/>
            </svg>`,

            // Device 6: Foldable Gaming Device
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad6" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899"/>
                        <stop offset="100%" style="stop-color:#be185d"/>
                    </linearGradient>
                    <linearGradient id="screenGrad6" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1f2937"/>
                        <stop offset="100%" style="stop-color:#111827"/>
                    </linearGradient>
                </defs>
                
                <!-- Top half -->
                <rect x="40" y="20" width="200" height="70" rx="15" fill="url(#bodyGrad6)" stroke="#be185d" stroke-width="2"/>
                
                <!-- Top screen -->
                <rect x="50" y="25" width="180" height="60" rx="10" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="55" y="30" width="170" height="50" rx="8" fill="url(#screenGrad6)"/>
                <rect x="60" y="35" width="160" height="40" rx="6" fill="#000"/>
                
                <!-- Top screen content -->
                <rect x="65" y="40" width="150" height="30" rx="4" fill="#312e81" opacity="0.3"/>
                <rect x="70" y="45" width="60" height="4" rx="2" fill="#10b981"/>
                <rect x="70" y="52" width="40" height="4" rx="2" fill="#f59e0b"/>
                <rect x="70" y="59" width="80" height="4" rx="2" fill="#ef4444"/>
                
                <!-- Map display -->
                <rect x="140" y="45" width="70" height="20" rx="3" fill="#1e40af" opacity="0.4"/>
                <circle cx="160" cy="52" r="3" fill="#10b981"/>
                <circle cx="175" cy="58" r="2" fill="#ef4444"/>
                
                <!-- Hinge -->
                <rect x="40" y="88" width="200" height="4" rx="2" fill="#9ca3af" stroke="#6b7280" stroke-width="1"/>
                <circle cx="60" cy="90" r="3" fill="#6b7280"/>
                <circle cx="80" cy="90" r="3" fill="#6b7280"/>
                <circle cx="200" cy="90" r="3" fill="#6b7280"/>
                <circle cx="220" cy="90" r="3" fill="#6b7280"/>
                
                <!-- Bottom half -->
                <rect x="40" y="92" width="200" height="70" rx="15" fill="url(#bodyGrad6)" stroke="#be185d" stroke-width="2"/>
                
                <!-- Bottom screen/touchpad -->
                <rect x="50" y="97" width="180" height="60" rx="10" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="55" y="102" width="170" height="50" rx="8" fill="#111827"/>
                <rect x="60" y="107" width="160" height="40" rx="6" fill="#000" opacity="0.8"/>
                
                <!-- Touch interface -->
                <rect x="65" y="112" width="150" height="30" rx="4" fill="#1e293b" opacity="0.6"/>
                
                <!-- Virtual D-pad -->
                <g transform="translate(90,127)">
                    <rect x="-10" y="-3" width="20" height="6" rx="3" fill="#374151" opacity="0.7"/>
                    <rect x="-3" y="-10" width="6" height="20" rx="3" fill="#374151" opacity="0.7"/>
                </g>
                
                <!-- Virtual buttons -->
                <circle cx="180" cy="120" r="8" fill="#dc2626" opacity="0.7"/>
                <circle cx="200" cy="115" r="8" fill="#2563eb" opacity="0.7"/>
                <circle cx="200" cy="125" r="8" fill="#16a34a" opacity="0.7"/>
                <circle cx="220" cy="120" r="8" fill="#ca8a04" opacity="0.7"/>
                
                <!-- Menu icons -->
                <rect x="120" y="115" width="12" height="8" rx="4" fill="#6b7280" opacity="0.6"/>
                <rect x="140" y="115" width="12" height="8" rx="4" fill="#6b7280" opacity="0.6"/>
                
                <!-- Speaker grilles -->
                <g transform="translate(45,25)">
                    <circle cx="0" cy="0" r="1" fill="#be185d"/>
                    <circle cx="4" cy="0" r="1" fill="#be185d"/>
                    <circle cx="8" cy="0" r="1" fill="#be185d"/>
                </g>
                <g transform="translate(227,25)">
                    <circle cx="0" cy="0" r="1" fill="#be185d"/>
                    <circle cx="4" cy="0" r="1" fill="#be185d"/>
                    <circle cx="8" cy="0" r="1" fill="#be185d"/>
                </g>
                
                <!-- Status indicators -->
                <circle cx="220" cy="35" r="2" fill="#10b981"/>
                <circle cx="225" cy="35" r="2" fill="#3b82f6"/>
                <circle cx="230" cy="35" r="2" fill="#f59e0b"/>
            </svg>`,

            // Device 7: Ergonomic Gaming Controller
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad7" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#059669"/>
                        <stop offset="100%" style="stop-color:#047857"/>
                    </linearGradient>
                    <radialGradient id="screenGrad7" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#1f2937"/>
                        <stop offset="100%" style="stop-color:#111827"/>
                    </radialGradient>
                </defs>
                
                <!-- Main body with ergonomic curves -->
                <path d="M50 40 Q30 40 30 60 L30 120 Q30 140 50 140 L90 140 Q110 140 110 120 L110 100 L170 100 Q170 120 170 120 L170 140 Q190 140 210 140 L230 140 Q250 140 250 120 L250 60 Q250 40 230 40 L50 40 Z" fill="url(#bodyGrad7)" stroke="#065f46" stroke-width="2"/>
                
                <!-- Central screen -->
                <rect x="120" y="50" width="120" height="80" rx="12" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="125" y="55" width="110" height="70" rx="8" fill="url(#screenGrad7)"/>
                <rect x="130" y="60" width="100" height="60" rx="6" fill="#000"/>
                
                <!-- Screen content -->
                <rect x="135" y="65" width="90" height="50" rx="4" fill="#1e40af" opacity="0.3"/>
                <circle cx="180" cy="90" r="15" fill="#06b6d4" opacity="0.5"/>
                <rect x="145" y="75" width="30" height="4" rx="2" fill="#10b981"/>
                <rect x="145" y="85" width="50" height="4" rx="2" fill="#f59e0b"/>
                <rect x="145" y="95" width="40" height="4" rx="2" fill="#ef4444"/>
                
                <!-- Left grip area -->
                <ellipse cx="70" cy="90" rx="25" ry="35" fill="#047857" stroke="#065f46" stroke-width="1"/>
                
                <!-- Left analog stick -->
                <circle cx="70" cy="75" r="12" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                <circle cx="70" cy="75" r="8" fill="#4b5563"/>
                <circle cx="72" cy="73" r="3" fill="#9ca3af"/>
                
                <!-- Left trigger -->
                <rect x="45" y="35" width="20" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                
                <!-- D-pad -->
                <g transform="translate(70,110)">
                    <rect x="-10" y="-3" width="20" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                    <rect x="-3" y="-10" width="6" height="20" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                </g>
                
                <!-- Right grip area -->
                <ellipse cx="210" cy="90" rx="25" ry="35" fill="#047857" stroke="#065f46" stroke-width="1"/>
                
                <!-- Right analog stick -->
                <circle cx="210" cy="105" r="12" fill="#1f2937" stroke="#374151" stroke-width="2"/>
                <circle cx="210" cy="105" r="8" fill="#4b5563"/>
                <circle cx="208" cy="103" r="3" fill="#9ca3af"/>
                
                <!-- Right trigger -->
                <rect x="215" y="35" width="20" height="8" rx="4" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                
                <!-- Action buttons -->
                <circle cx="190" cy="70" r="8" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                <text x="190" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">A</text>
                
                <circle cx="210" cy="60" r="8" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                <text x="210" y="65" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">B</text>
                
                <circle cx="210" cy="80" r="8" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                <text x="210" y="85" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">X</text>
                
                <circle cx="230" cy="70" r="8" fill="#ca8a04" stroke="#a16207" stroke-width="1"/>
                <text x="230" y="75" text-anchor="middle" fill="white" font-family="Roboto" font-size="7" font-weight="bold">Y</text>
                
                <!-- Menu buttons -->
                <rect x="110" y="65" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="158" y="65" width="12" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                
                <!-- Shoulder buttons -->
                <rect x="50" y="45" width="15" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                <rect x="215" y="45" width="15" height="6" rx="3" fill="#1f2937" stroke="#374151" stroke-width="1"/>
                
                <!-- Grip texture -->
                <g transform="translate(55,100)">
                    <circle cx="0" cy="0" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="0" cy="8" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="0" cy="16" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="8" cy="4" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="8" cy="12" r="1" fill="#065f46" opacity="0.6"/>
                </g>
                <g transform="translate(217,100)">
                    <circle cx="0" cy="0" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="0" cy="8" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="0" cy="16" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="-8" cy="4" r="1" fill="#065f46" opacity="0.6"/>
                    <circle cx="-8" cy="12" r="1" fill="#065f46" opacity="0.6"/>
                </g>
                
                <!-- Brand logo -->
                <rect x="170" y="135" width="20" height="3" rx="1.5" fill="#10b981" opacity="0.7"/>
            </svg>`,

            // Device 8: Minimalist Gaming Device
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad8" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f1f5f9"/>
                        <stop offset="100%" style="stop-color:#e2e8f0"/>
                    </linearGradient>
                    <linearGradient id="screenGrad8" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#0f172a"/>
                        <stop offset="100%" style="stop-color:#1e293b"/>
                    </linearGradient>
                </defs>
                
                <!-- Main body -->
                <rect x="50" y="40" width="180" height="100" rx="20" fill="url(#bodyGrad8)" stroke="#cbd5e1" stroke-width="2"/>
                
                <!-- Screen -->
                <rect x="65" y="50" width="150" height="80" rx="12" fill="url(#screenGrad8)" stroke="#475569" stroke-width="1"/>
                <rect x="70" y="55" width="140" height="70" rx="8" fill="#000"/>
                
                <!-- Screen content - clean UI -->
                <rect x="75" y="60" width="130" height="60" rx="6" fill="#1e293b" opacity="0.9"/>
                
                <!-- Minimal game interface -->
                <circle cx="140" cy="90" r="20" fill="#3b82f6" opacity="0.3"/>
                <circle cx="140" cy="90" r="12" fill="#1d4ed8" opacity="0.5"/>
                <circle cx="140" cy="90" r="6" fill="#1e40af" opacity="0.7"/>
                
                <!-- Clean HUD -->
                <rect x="85" y="70" width="40" height="3" rx="1.5" fill="#10b981"/>
                <rect x="85" y="78" width="25" height="3" rx="1.5" fill="#f59e0b"/>
                <rect x="155" y="70" width="50" height="3" rx="1.5" fill="#ef4444"/>
                
                <!-- Score -->
                <rect x="180" y="105" width="20" height="8" rx="4" fill="#1f2937"/>
                <text x="190" y="110" text-anchor="middle" fill="#10b981" font-family="Roboto" font-size="5" font-weight="300">999</text>
                
                <!-- Left controls - minimal -->
                <circle cx="80" cy="100" r="8" fill="#64748b" stroke="#475569" stroke-width="1"/>
                <circle cx="80" cy="100" r="4" fill="#94a3b8"/>
                
                <!-- Right controls - minimal -->
                <circle cx="200" cy="80" r="6" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                <circle cx="200" cy="100" r="6" fill="#3b82f6" stroke="#2563eb" stroke-width="1"/>
                
                <!-- D-pad - simplified -->
                <g transform="translate(80,120)">
                    <rect x="-6" y="-2" width="12" height="4" rx="2" fill="#64748b"/>
                    <rect x="-2" y="-6" width="4" height="12" rx="2" fill="#64748b"/>
                </g>
                
                <!-- Menu button -->
                <rect x="130" y="85" width="20" height="6" rx="3" fill="#64748b" stroke="#475569" stroke-width="1"/>
                
                <!-- Power button -->
                <circle cx="220" cy="50" r="3" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
                
                <!-- USB-C port -->
                <rect x="135" y="138" width="10" height="3" rx="1.5" fill="#64748b"/>
                
                <!-- Minimal branding -->
                <rect x="120" y="145" width="40" height="1" rx="0.5" fill="#94a3b8" opacity="0.6"/>
                
                <!-- Clean speaker grilles -->
                <g transform="translate(60,50)">
                    <circle cx="0" cy="0" r="0.5" fill="#94a3b8"/>
                    <circle cx="3" cy="0" r="0.5" fill="#94a3b8"/>
                    <circle cx="6" cy="0" r="0.5" fill="#94a3b8"/>
                </g>
                <g transform="translate(211,50)">
                    <circle cx="0" cy="0" r="0.5" fill="#94a3b8"/>
                    <circle cx="3" cy="0" r="0.5" fill="#94a3b8"/>
                    <circle cx="6" cy="0" r="0.5" fill="#94a3b8"/>
                </g>
            </svg>`,

            // Device 9: Gaming Smartphone
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad9" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1f2937"/>
                        <stop offset="100%" style="stop-color:#111827"/>
                    </linearGradient>
                    <linearGradient id="screenGrad9" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#000"/>
                        <stop offset="100%" style="stop-color:#1a1a1a"/>
                    </linearGradient>
                    <linearGradient id="accentGrad9" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f59e0b"/>
                        <stop offset="100%" style="stop-color:#d97706"/>
                    </linearGradient>
                </defs>
                
                <!-- Phone body -->
                <rect x="90" y="30" width="100" height="120" rx="18" fill="url(#bodyGrad9)" stroke="#374151" stroke-width="2"/>
                
                <!-- Screen -->
                <rect x="95" y="40" width="90" height="100" rx="12" fill="url(#screenGrad9)" stroke="#1f2937" stroke-width="1"/>
                <rect x="100" y="45" width="80" height="90" rx="8" fill="#000"/>
                
                <!-- Screen content -->
                <rect x="105" y="50" width="70" height="80" rx="6" fill="#1e293b" opacity="0.9"/>
                
                <!-- Mobile game UI -->
                <rect x="110" y="55" width="60" height="70" rx="4" fill="#0c4a6e" opacity="0.2"/>
                <circle cx="140" cy="90" r="15" fill="#f59e0b" opacity="0.4"/>
                <circle cx="140" cy="90" r="8" fill="#d97706" opacity="0.6"/>
                
                <!-- Mobile HUD -->
                <rect x="115" y="60" width="25" height="3" rx="1.5" fill="#10b981"/>
                <rect x="115" y="67" width="35" height="3" rx="1.5" fill="#ef4444"/>
                <rect x="145" y="60" width="20" height="3" rx="1.5" fill="#3b82f6"/>
                
                <!-- Touch controls overlay -->
                <circle cx="120" cy="110" r="8" fill="#f59e0b" opacity="0.3" stroke="#d97706" stroke-width="1"/>
                <circle cx="160" cy="110" r="8" fill="#f59e0b" opacity="0.3" stroke="#d97706" stroke-width="1"/>
                
                <!-- Virtual joystick -->
                <circle cx="120" cy="110" r="5" fill="#d97706" opacity="0.6"/>
                <circle cx="122" cy="108" r="2" fill="#f59e0b"/>
                
                <!-- Action button -->
                <circle cx="160" cy="110" r="5" fill="#dc2626" opacity="0.6"/>
                <text x="160" y="113" text-anchor="middle" fill="white" font-family="Roboto" font-size="4" font-weight="bold">A</text>
                
                <!-- Left attachment -->
                <rect x="60" y="50" width="35" height="80" rx="12" fill="url(#bodyGrad9)" stroke="#374151" stroke-width="2"/>
                
                <!-- Left analog stick -->
                <circle cx="77" cy="75" r="10" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                <circle cx="77" cy="75" r="6" fill="#6b7280"/>
                <circle cx="79" cy="73" r="2" fill="#9ca3af"/>
                
                <!-- Left D-pad -->
                <g transform="translate(77,105)">
                    <rect x="-6" y="-2" width="12" height="4" rx="2" fill="#374151"/>
                    <rect x="-2" y="-6" width="4" height="12" rx="2" fill="#374151"/>
                </g>
                
                <!-- Right attachment -->
                <rect x="185" y="50" width="35" height="80" rx="12" fill="url(#bodyGrad9)" stroke="#374151" stroke-width="2"/>
                
                <!-- Right buttons -->
                <circle cx="203" cy="70" r="6" fill="#dc2626" stroke="#991b1b" stroke-width="1"/>
                <circle cx="203" cy="85" r="6" fill="#2563eb" stroke="#1d4ed8" stroke-width="1"/>
                <circle cx="203" cy="100" r="6" fill="#16a34a" stroke="#15803d" stroke-width="1"/>
                
                <!-- Right analog stick -->
                <circle cx="203" cy="115" r="8" fill="#374151" stroke="#4b5563" stroke-width="1"/>
                <circle cx="203" cy="115" r="5" fill="#6b7280"/>
                <circle cx="201" cy="113" r="1.5" fill="#9ca3af"/>
                
                <!-- Phone details -->
                <circle cx="140" cy="35" r="2" fill="#374151"/>
                <rect x="130" y="145" width="20" height="3" rx="1.5" fill="#374151"/>
                
                <!-- Gaming mode indicator -->
                <rect x="170" y="32" width="8" height="3" rx="1.5" fill="url(#accentGrad9)"/>
            </svg>`,

            // Device 10: Futuristic Gaming Device
            `<svg width="280" height="180" viewBox="0 0 280 180" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bodyGrad10" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366f1"/>
                        <stop offset="100%" style="stop-color:#4f46e5"/>
                    </linearGradient>
                    <radialGradient id="screenGrad10" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#000"/>
                        <stop offset="100%" style="stop-color:#1a1a1a"/>
                    </radialGradient>
                    <linearGradient id="hologramGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#06b6d4"/>
                        <stop offset="50%" style="stop-color:#8b5cf6"/>
                        <stop offset="100%" style="stop-color:#ec4899"/>
                    </linearGradient>
                </defs>
                
                <!-- Main body with futuristic curves -->
                <path d="M40 35 Q20 35 20 55 L20 125 Q20 145 40 145 L240 145 Q260 145 260 125 L260 55 Q260 35 240 35 L40 35 Z" fill="url(#bodyGrad10)" stroke="#4338ca" stroke-width="2"/>
                
                <!-- Holographic accent lines -->
                <rect x="25" y="40" width="230" height="1" rx="0.5" fill="url(#hologramGrad)" opacity="0.8"/>
                <rect x="25" y="139" width="230" height="1" rx="0.5" fill="url(#hologramGrad)" opacity="0.8"/>
                
                <!-- Central holographic display -->
                <rect x="50" y="45" width="180" height="90" rx="15" fill="#1a1a1a" stroke="#4338ca" stroke-width="1"/>
                <rect x="55" y="50" width="170" height="80" rx="10" fill="url(#screenGrad10)"/>
                <rect x="60" y="55" width="160" height="70" rx="8" fill="#000"/>
                
                <!-- Holographic UI -->
                <rect x="65" y="60" width="150" height="60" rx="6" fill="#1e1b4b" opacity="0.3"/>
                
                <!-- 3D holographic elements -->
                <circle cx="140" cy="90" r="20" fill="url(#hologramGrad)" opacity="0.4"/>
                <circle cx="140" cy="90" r="12" fill="#06b6d4" opacity="0.6"/>
                <circle cx="140" cy="90" r="6" fill="#8b5cf6" opacity="0.8"/>
                
                <!-- Floating UI elements -->
                <rect x="75" y="70" width="30" height="3" rx="1.5" fill="#06b6d4" opacity="0.8"/>
                <rect x="75" y="78" width="45" height="3" rx="1.5" fill="#8b5cf6" opacity="0.8"/>
                <rect x="75" y="86" width="25" height="3" rx="1.5" fill="#ec4899" opacity="0.8"/>
                
                <!-- Holographic data streams -->
                <rect x="175" y="70" width="40" height="3" rx="1.5" fill="#10b981" opacity="0.7"/>
                <rect x="175" y="78" width="30" height="3" rx="1.5" fill="#f59e0b" opacity="0.7"/>
                <rect x="175" y="86" width="50" height="3" rx="1.5" fill="#ef4444" opacity="0.7"/>
                
                <!-- Neural interface ports -->
                <circle cx="80" cy="105" r="8" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                <circle cx="80" cy="105" r="4" fill="#8b5cf6" opacity="0.8"/>
                
                <circle cx="200" cy="105" r="8" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                <circle cx="200" cy="105" r="4" fill="#8b5cf6" opacity="0.8"/>
                
                <!-- Quantum control pads -->
                <g transform="translate(80,125)">
                    <rect x="-10" y="-3" width="20" height="6" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                    <rect x="-3" y="-10" width="6" height="20" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                    <circle cx="0" cy="0" r="2" fill="url(#hologramGrad)"/>
                </g>
                
                <!-- Plasma action buttons -->
                <circle cx="190" cy="70" r="8" fill="#dc2626" stroke="#ef4444" stroke-width="1"/>
                <circle cx="190" cy="70" r="4" fill="#fca5a5" opacity="0.8"/>
                
                <circle cx="210" cy="60" r="8" fill="#2563eb" stroke="#3b82f6" stroke-width="1"/>
                <circle cx="210" cy="60" r="4" fill="#93c5fd" opacity="0.8"/>
                
                <circle cx="210" cy="80" r="8" fill="#16a34a" stroke="#22c55e" stroke-width="1"/>
                <circle cx="210" cy="80" r="4" fill="#86efac" opacity="0.8"/>
                
                <circle cx="230" cy="70" r="8" fill="#ca8a04" stroke="#eab308" stroke-width="1"/>
                <circle cx="230" cy="70" r="4" fill="#fde047" opacity="0.8"/>
                
                <!-- Biometric sensors -->
                <rect x="120" y="65" width="20" height="6" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                <rect x="140" y="65" width="20" height="6" rx="3" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                
                <!-- Energy cores -->
                <circle cx="50" cy="90" r="12" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                <circle cx="50" cy="90" r="6" fill="url(#hologramGrad)" opacity="0.8"/>
                
                <circle cx="230" cy="90" r="12" fill="#4338ca" stroke="#6366f1" stroke-width="2"/>
                <circle cx="230" cy="90" r="6" fill="url(#hologramGrad)" opacity="0.8"/>
                
                <!-- Quantum processors -->
                <rect x="30" y="50" width="15" height="8" rx="4" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                <rect x="235" y="50" width="15" height="8" rx="4" fill="#4338ca" stroke="#6366f1" stroke-width="1"/>
                
                <!-- Holographic projectors -->
                <circle cx="70" cy="45" r="3" fill="#06b6d4"/>
                <circle cx="140" cy="40" r="3" fill="#8b5cf6"/>
                <circle cx="210" cy="45" r="3" fill="#ec4899"/>
                
                <!-- Data streams -->
                <g transform="translate(40,140)">
                    <rect x="0" y="0" width="1" height="6" rx="0.5" fill="#06b6d4" opacity="0.6"/>
                    <rect x="3" y="0" width="1" height="6" rx="0.5" fill="#8b5cf6" opacity="0.6"/>
                    <rect x="6" y="0" width="1" height="6" rx="0.5" fill="#ec4899" opacity="0.6"/>
                </g>
                <g transform="translate(235,140)">
                    <rect x="0" y="0" width="1" height="6" rx="0.5" fill="#06b6d4" opacity="0.6"/>
                    <rect x="3" y="0" width="1" height="6" rx="0.5" fill="#8b5cf6" opacity="0.6"/>
                    <rect x="6" y="0" width="1" height="6" rx="0.5" fill="#ec4899" opacity="0.6"/>
                </g>
                
                <!-- Quantum signature -->
                <rect x="120" y="150" width="40" height="2" rx="1" fill="url(#hologramGrad)" opacity="0.8"/>
            </svg>`
        ];

        // Copy SVG function
        function copySVG(button, index) {
            const svgCode = svgCodes[index];
            
            // Create a temporary textarea to copy the text
            const textarea = document.createElement('textarea');
            textarea.value = svgCode;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            
            // Update button text and style
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            button.classList.add('copied');
            
            // Reset button after 2 seconds
            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('copied');
            }, 2000);
        }
    </script>
</body>
</html>
