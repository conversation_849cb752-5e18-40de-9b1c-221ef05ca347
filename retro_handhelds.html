<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Handheld Game Devices</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f7fa;
            color: #333;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
            font-size: 2.5rem;
        }
        
        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .device-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .device-card:hover {
            transform: translateY(-5px);
        }
        
        .device-title {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            text-align: center;
            color: #2c3e50;
        }
        
        .svg-container {
            display: flex;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .copy-btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            font-family: 'Roboto', sans-serif;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #2980b9;
        }
        
        .copy-btn.copied {
            background: #27ae60;
        }
        
        @media (max-width: 768px) {
            .devices-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Retro Handheld Game Devices</h1>
        <div class="devices-grid">
            <!-- Device 1: Classic Game Boy -->
            <div class="device-card">
                <h3 class="device-title">Classic Game Boy</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="30" width="160" height="140" rx="10" fill="#c0c0c0" stroke="#333" stroke-width="2"/>
                        <rect x="30" y="40" width="140" height="80" fill="#8a9a8b"/>
                        <circle cx="60" cy="150" r="12" fill="#333"/>
                        <circle cx="140" cy="150" r="12" fill="#333"/>
                        <rect x="80" y="140" width="40" height="20" rx="5" fill="#333"/>
                        <rect x="85" y="25" width="30" height="5" rx="2" fill="#ff6b6b"/>
                        <rect x="100" y="15" width="40" height="10" rx="3" fill="#4ecdc4"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="20" y="30" width="160" height="140" rx="10" fill="#c0c0c0" stroke="#333" stroke-width="2"/><rect x="30" y="40" width="140" height="80" fill="#8a9a8b"/><circle cx="60" cy="150" r="12" fill="#333"/><circle cx="140" cy="150" r="12" fill="#333"/><rect x="80" y="140" width="40" height="20" rx="5" fill="#333"/><rect x="85" y="25" width="30" height="5" rx="2" fill="#ff6b6b"/><rect x="100" y="15" width="40" height="10" rx="3" fill="#4ecdc4"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 2: Game Gear -->
            <div class="device-card">
                <h3 class="device-title">Sega Game Gear</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="25" y="20" width="150" height="160" rx="8" fill="#1e88e5" stroke="#0d47a1" stroke-width="2"/>
                        <rect x="35" y="30" width="130" height="100" fill="#000"/>
                        <circle cx="60" cy="150" r="10" fill="#f44336"/>
                        <circle cx="100" cy="150" r="10" fill="#4caf50"/>
                        <circle cx="140" cy="150" r="10" fill="#ffeb3b"/>
                        <rect x="45" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/>
                        <rect x="135" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="25" y="20" width="150" height="160" rx="8" fill="#1e88e5" stroke="#0d47a1" stroke-width="2"/><rect x="35" y="30" width="130" height="100" fill="#000"/><circle cx="60" cy="150" r="10" fill="#f44336"/><circle cx="100" cy="150" r="10" fill="#4caf50"/><circle cx="140" cy="150" r="10" fill="#ffeb3b"/><rect x="45" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/><rect x="135" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 3: Neo Geo Pocket -->
            <div class="device-card">
                <h3 class="device-title">Neo Geo Pocket</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="30" y="25" width="140" height="150" rx="15" fill="#f44336" stroke="#b71c1c" stroke-width="2"/>
                        <rect x="40" y="35" width="120" height="90" fill="#212121"/>
                        <circle cx="70" cy="145" r="8" fill="#ff9800"/>
                        <circle cx="100" cy="145" r="8" fill="#ff9800"/>
                        <circle cx="130" cy="145" r="8" fill="#ff9800"/>
                        <rect x="50" y="135" width="20" height="4" rx="2" fill="#9e9e9e"/>
                        <rect x="130" y="135" width="20" height="4" rx="2" fill="#9e9e9e"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="30" y="25" width="140" height="150" rx="15" fill="#f44336" stroke="#b71c1c" stroke-width="2"/><rect x="40" y="35" width="120" height="90" fill="#212121"/><circle cx="70" cy="145" r="8" fill="#ff9800"/><circle cx="100" cy="145" r="8" fill="#ff9800"/><circle cx="130" cy="145" r="8" fill="#ff9800"/><rect x="50" y="135" width="20" height="4" rx="2" fill="#9e9e9e"/><rect x="130" y="135" width="20" height="4" rx="2" fill="#9e9e9e"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 4: Atari Lynx -->
            <div class="device-card">
                <h3 class="device-title">Atari Lynx</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="30" width="160" height="140" rx="10" fill="#ff9800" stroke="#e65100" stroke-width="2"/>
                        <rect x="30" y="40" width="140" height="90" fill="#000"/>
                        <circle cx="60" cy="150" r="10" fill="#795548"/>
                        <circle cx="100" cy="150" r="10" fill="#795548"/>
                        <circle cx="140" cy="150" r="10" fill="#795548"/>
                        <rect x="70" y="140" width="60" height="15" rx="3" fill="#9e9e9e"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="20" y="30" width="160" height="140" rx="10" fill="#ff9800" stroke="#e65100" stroke-width="2"/><rect x="30" y="40" width="140" height="90" fill="#000"/><circle cx="60" cy="150" r="10" fill="#795548"/><circle cx="100" cy="150" r="10" fill="#795548"/><circle cx="140" cy="150" r="10" fill="#795548"/><rect x="70" y="140" width="60" height="15" rx="3" fill="#9e9e9e"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 5: WonderSwan -->
            <div class="device-card">
                <h3 class="device-title">WonderSwan</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="30" y="25" width="140" height="150" rx="12" fill="#4caf50" stroke="#2e7d32" stroke-width="2"/>
                        <rect x="40" y="35" width="120" height="100" fill="#000"/>
                        <circle cx="80" cy="150" r="8" fill="#f44336"/>
                        <circle cx="120" cy="150" r="8" fill="#f44336"/>
                        <rect x="50" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/>
                        <rect x="130" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="30" y="25" width="140" height="150" rx="12" fill="#4caf50" stroke="#2e7d32" stroke-width="2"/><rect x="40" y="35" width="120" height="100" fill="#000"/><circle cx="80" cy="150" r="8" fill="#f44336"/><circle cx="120" cy="150" r="8" fill="#f44336"/><rect x="50" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/><rect x="130" y="140" width="20" height="5" rx="2" fill="#9e9e9e"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 6: TurboExpress -->
            <div class="device-card">
                <h3 class="device-title">TurboExpress</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="25" y="20" width="150" height="160" rx="10" fill="#9c27b0" stroke="#6a1b9a" stroke-width="2"/>
                        <rect x="35" y="30" width="130" height="110" fill="#000"/>
                        <circle cx="60" cy="155" r="9" fill="#ff9800"/>
                        <circle cx="100" cy="155" r="9" fill="#ff9800"/>
                        <circle cx="140" cy="155" r="9" fill="#ff9800"/>
                        <rect x="45" y="145" width="20" height="4" rx="2" fill="#9e9e9e"/>
                        <rect x="135" y="145" width="20" height="4" rx="2" fill="#9e9e9e"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="25" y="20" width="150" height="160" rx="10" fill="#9c27b0" stroke="#6a1b9a" stroke-width="2"/><rect x="35" y="30" width="130" height="110" fill="#000"/><circle cx="60" cy="155" r="9" fill="#ff9800"/><circle cx="100" cy="155" r="9" fill="#ff9800"/><circle cx="140" cy="155" r="9" fill="#ff9800"/><rect x="45" y="145" width="20" height="4" rx="2" fill="#9e9e9e"/><rect x="135" y="145" width="20" height="4" rx="2" fill="#9e9e9e"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 7: Virtual Boy -->
            <div class="device-card">
                <h3 class="device-title">Virtual Boy</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="30" y="40" width="140" height="120" rx="10" fill="#b71c1c" stroke="#7f0000" stroke-width="2"/>
                        <rect x="40" y="50" width="120" height="70" fill="#000"/>
                        <rect x="20" y="80" width="10" height="40" rx="3" fill="#212121"/>
                        <rect x="170" y="80" width="10" height="40" rx="3" fill="#212121"/>
                        <rect x="70" y="140" width="60" height="20" rx="5" fill="#333"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="30" y="40" width="140" height="120" rx="10" fill="#b71c1c" stroke="#7f0000" stroke-width="2"/><rect x="40" y="50" width="120" height="70" fill="#000"/><rect x="20" y="80" width="10" height="40" rx="3" fill="#212121"/><rect x="170" y="80" width="10" height="40" rx="3" fill="#212121"/><rect x="70" y="140" width="60" height="20" rx="5" fill="#333"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 8: Nintendo DS Lite -->
            <div class="device-card">
                <h3 class="device-title">Nintendo DS Lite</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="30" width="160" height="140" rx="8" fill="#e0e0e0" stroke="#bdbdbd" stroke-width="2"/>
                        <rect x="25" y="35" width="70" height="60" fill="#000"/>
                        <rect x="105" y="35" width="70" height="60" fill="#000"/>
                        <rect x="25" y="110" width="150" height="20" rx="3" fill="#9e9e9e"/>
                        <circle cx="60" cy="145" r="6" fill="#f44336"/>
                        <circle cx="85" cy="145" r="6" fill="#4caf50"/>
                        <circle cx="110" cy="145" r="6" fill="#ffeb3b"/>
                        <circle cx="135" cy="145" r="6" fill="#2196f3"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="20" y="30" width="160" height="140" rx="8" fill="#e0e0e0" stroke="#bdbdbd" stroke-width="2"/><rect x="25" y="35" width="70" height="60" fill="#000"/><rect x="105" y="35" width="70" height="60" fill="#000"/><rect x="25" y="110" width="150" height="20" rx="3" fill="#9e9e9e"/><circle cx="60" cy="145" r="6" fill="#f44336"/><circle cx="85" cy="145" r="6" fill="#4caf50"/><circle cx="110" cy="145" r="6" fill="#ffeb3b"/><circle cx="135" cy="145" r="6" fill="#2196f3"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 9: PSP -->
            <div class="device-card">
                <h3 class="device-title">PlayStation Portable</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="25" y="30" width="150" height="140" rx="10" fill="#212121" stroke="#000" stroke-width="2"/>
                        <rect x="35" y="40" width="130" height="80" fill="#000"/>
                        <circle cx="70" cy="140" r="8" fill="#9e9e9e"/>
                        <circle cx="100" cy="140" r="8" fill="#9e9e9e"/>
                        <circle cx="130" cy="140" r="8" fill="#9e9e9e"/>
                        <rect x="50" y="130" width="20" height="5" rx="2" fill="#f44336"/>
                        <rect x="130" y="130" width="20" height="5" rx="2" fill="#4caf50"/>
                        <rect x="80" y="25" width="40" height="5" rx="2" fill="#ff9800"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="25" y="30" width="150" height="140" rx="10" fill="#212121" stroke="#000" stroke-width="2"/><rect x="35" y="40" width="130" height="80" fill="#000"/><circle cx="70" cy="140" r="8" fill="#9e9e9e"/><circle cx="100" cy="140" r="8" fill="#9e9e9e"/><circle cx="130" cy="140" r="8" fill="#9e9e9e"/><rect x="50" y="130" width="20" height="5" rx="2" fill="#f44336"/><rect x="130" y="130" width="20" height="5" rx="2" fill="#4caf50"/><rect x="80" y="25" width="40" height="5" rx="2" fill="#ff9800"/></svg>'>Copy SVG Code</button>
            </div>

            <!-- Device 10: Sega Nomad -->
            <div class="device-card">
                <h3 class="device-title">Sega Nomad</h3>
                <div class="svg-container">
                    <svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="30" width="160" height="140" rx="10" fill="#2196f3" stroke="#0d47a1" stroke-width="2"/>
                        <rect x="30" y="40" width="140" height="90" fill="#000"/>
                        <circle cx="60" cy="150" r="10" fill="#ff9800"/>
                        <circle cx="100" cy="150" r="10" fill="#ff9800"/>
                        <circle cx="140" cy="150" r="10" fill="#ff9800"/>
                        <rect x="70" y="140" width="60" height="15" rx="3" fill="#e0e0e0"/>
                        <rect x="85" y="25" width="30" height="5" rx="2" fill="#f44336"/>
                    </svg>
                </div>
                <button class="copy-btn" data-svg='<svg width="180" height="180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><rect x="20" y="30" width="160" height="140" rx="10" fill="#2196f3" stroke="#0d47a1" stroke-width="2"/><rect x="30" y="40" width="140" height="90" fill="#000"/><circle cx="60" cy="150" r="10" fill="#ff9800"/><circle cx="100" cy="150" r="10" fill="#ff9800"/><circle cx="140" cy="150" r="10" fill="#ff9800"/><rect x="70" y="140" width="60" height="15" rx="3" fill="#e0e0e0"/><rect x="85" y="25" width="30" height="5" rx="2" fill="#f44336"/></svg>'>Copy SVG Code</button>
            </div>
        </div>
    </div>

    <script>
        document.querySelectorAll('.copy-btn').forEach(button => {
            button.addEventListener('click', function() {
                const svgCode = this.getAttribute('data-svg');
                
                // Create temporary textarea to copy from
                const textarea = document.createElement('textarea');
                textarea.value = svgCode;
                document.body.appendChild(textarea);
                textarea.select();
                
                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        const originalText = this.textContent;
                        this.textContent = 'Copied!';
                        this.classList.add('copied');
                        
                        setTimeout(() => {
                            this.textContent = originalText;
                            this.classList.remove('copied');
                        }, 2000);
                    }
                } catch (err) {
                    console.error('Failed to copy: ', err);
                }
                
                document.body.removeChild(textarea);
            });
        });
    </script>
</body>
</html>
