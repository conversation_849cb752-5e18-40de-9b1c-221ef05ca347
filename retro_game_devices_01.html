<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Game Device SVGs</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .devices-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .device-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .device-svg {
            width: 100%;
            height: auto;
            margin-bottom: 15px;
        }
        .copy-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .copy-btn:hover {
            background-color: #45a049;
        }
        .device-title {
            margin: 10px 0;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>Retro Game Device SVGs</h1>
    
    <div class="devices-container">
        <!-- Device 1 -->
        <div class="device-card">
            <h3 class="device-title">Classic Game Boy</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="10" fill="#a0a0a0"/>
                <rect x="30" y="30" width="140" height="90" rx="5" fill="#d0d0d0"/>
                <rect x="40" y="40" width="120" height="60" fill="#333"/>
                <circle cx="60" cy="110" r="8" fill="#ff5555"/>
                <circle cx="80" cy="110" r="8" fill="#55ff55"/>
                <circle cx="100" cy="110" r="8" fill="#5555ff"/>
                <circle cx="120" cy="110" r="8" fill="#ffff55"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#a0a0a0"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg1')">Copy SVG Code</button>
        </div>

        <!-- Device 2 -->
        <div class="device-card">
            <h3 class="device-title">Neon Pocket</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="15" fill="#222"/>
                <rect x="30" y="30" width="140" height="90" rx="10" fill="#444"/>
                <rect x="40" y="40" width="120" height="60" fill="#111"/>
                <circle cx="60" cy="110" r="8" fill="#ff00ff"/>
                <circle cx="80" cy="110" r="8" fill="#00ffff"/>
                <circle cx="100" cy="110" r="8" fill="#ffff00"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#666"/>
                <path d="M50 50 L150 50 L150 90 L50 90 Z" fill="none" stroke="#ff00ff" stroke-width="2"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg2')">Copy SVG Code</button>
        </div>

        <!-- Device 3 -->
        <div class="device-card">
            <h3 class="device-title">Pixel Master</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="5" fill="#5566aa"/>
                <rect x="30" y="30" width="140" height="90" rx="3" fill="#7788cc"/>
                <rect x="40" y="40" width="120" height="60" fill="#334466"/>
                <rect x="50" y="110" width="20" height="10" fill="#ff3366"/>
                <rect x="80" y="110" width="20" height="10" fill="#33ff66"/>
                <rect x="110" y="110" width="20" height="10" fill="#3366ff"/>
                <rect x="140" y="80" width="30" height="20" rx="2" fill="#99aadd"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg3')">Copy SVG Code</button>
        </div>

        <!-- Device 4 -->
        <div class="device-card">
            <h3 class="device-title">Retro Advance</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="12" fill="#e74c3c"/>
                <rect x="30" y="30" width="140" height="90" rx="8" fill="#c0392b"/>
                <rect x="40" y="40" width="120" height="60" fill="#2c3e50"/>
                <circle cx="70" cy="110" r="7" fill="#f1c40f"/>
                <circle cx="90" cy="110" r="7" fill="#2ecc71"/>
                <circle cx="110" cy="110" r="7" fill="#3498db"/>
                <rect x="140" y="80" width="30" height="20" rx="4" fill="#e67e22"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg4')">Copy SVG Code</button>
        </div>

        <!-- Device 5 -->
        <div class="device-card">
            <h3 class="device-title">Cyber Handheld</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="8" fill="#2c3e50"/>
                <rect x="30" y="30" width="140" height="90" rx="6" fill="#34495e"/>
                <rect x="40" y="40" width="120" height="60" fill="#1a1a1a"/>
                <path d="M60 110 A10 10 0 0 1 80 110" fill="none" stroke="#e74c3c" stroke-width="3"/>
                <path d="M90 110 A10 10 0 0 1 110 110" fill="none" stroke="#2ecc71" stroke-width="3"/>
                <path d="M120 110 A10 10 0 0 1 140 110" fill="none" stroke="#3498db" stroke-width="3"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#7f8c8d"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg5')">Copy SVG Code</button>
        </div>

        <!-- Device 6 -->
        <div class="device-card">
            <h3 class="device-title">Pastel Player</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="10" fill="#ffb6c1"/>
                <rect x="30" y="30" width="140" height="90" rx="8" fill="#ffd700"/>
                <rect x="40" y="40" width="120" height="60" fill="#87cefa"/>
                <circle cx="65" cy="110" r="6" fill="#98fb98"/>
                <circle cx="85" cy="110" r="6" fill="#ffa07a"/>
                <circle cx="105" cy="110" r="6" fill="#da70d6"/>
                <circle cx="125" cy="110" r="6" fill="#ff6347"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#ffb6c1"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg6')">Copy SVG Code</button>
        </div>

        <!-- Device 7 -->
        <div class="device-card">
            <h3 class="device-title">Mono Classic</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="5" fill="#333"/>
                <rect x="30" y="30" width="140" height="90" rx="3" fill="#555"/>
                <rect x="40" y="40" width="120" height="60" fill="#222"/>
                <rect x="50" y="110" width="15" height="8" fill="#fff"/>
                <rect x="75" y="110" width="15" height="8" fill="#fff"/>
                <rect x="100" y="110" width="15" height="8" fill="#fff"/>
                <rect x="125" y="110" width="15" height="8" fill="#fff"/>
                <rect x="140" y="80" width="30" height="20" rx="2" fill="#777"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg7')">Copy SVG Code</button>
        </div>

        <!-- Device 8 -->
        <div class="device-card">
            <h3 class="device-title">Gradient Warrior</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#ff512f;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#dd2476;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect x="20" y="20" width="160" height="110" rx="10" fill="url(#grad1)"/>
                <rect x="30" y="30" width="140" height="90" rx="7" fill="#fff" opacity="0.2"/>
                <rect x="40" y="40" width="120" height="60" fill="#000" opacity="0.7"/>
                <circle cx="70" cy="110" r="7" fill="#00f260"/>
                <circle cx="100" cy="110" r="7" fill="#0575e6"/>
                <circle cx="130" cy="110" r="7" fill="#f79d00"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#fff" opacity="0.3"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg8')">Copy SVG Code</button>
        </div>

        <!-- Device 9 -->
        <div class="device-card">
            <h3 class="device-title">Geometric Pro</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <polygon points="20,20 180,20 160,130 40,130" fill="#3498db"/>
                <rect x="30" y="30" width="140" height="90" rx="5" fill="#2980b9"/>
                <rect x="40" y="40" width="120" height="60" fill="#2c3e50"/>
                <polygon points="60,110 70,100 80,110 70,120" fill="#e74c3c"/>
                <polygon points="90,110 100,100 110,110 100,120" fill="#2ecc71"/>
                <polygon points="120,110 130,100 140,110 130,120" fill="#f1c40f"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#7f8c8d"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg9')">Copy SVG Code</button>
        </div>

        <!-- Device 10 -->
        <div class="device-card">
            <h3 class="device-title">Transparent Edge</h3>
            <svg class="device-svg" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" width="160" height="110" rx="10" fill="#fff" opacity="0.8" stroke="#333" stroke-width="2"/>
                <rect x="30" y="30" width="140" height="90" rx="5" fill="#fff" opacity="0.6"/>
                <rect x="40" y="40" width="120" height="60" fill="#000" opacity="0.3"/>
                <circle cx="60" cy="110" r="6" fill="#9b59b6" opacity="0.8"/>
                <circle cx="80" cy="110" r="6" fill="#e67e22" opacity="0.8"/>
                <circle cx="100" cy="110" r="6" fill="#1abc9c" opacity="0.8"/>
                <circle cx="120" cy="110" r="6" fill="#e74c3c" opacity="0.8"/>
                <rect x="140" y="80" width="30" height="20" rx="3" fill="#fff" opacity="0.5" stroke="#333" stroke-width="1"/>
            </svg>
            <button class="copy-btn" onclick="copyToClipboard('svg10')">Copy SVG Code</button>
        </div>
    </div>

    <script>
        function copyToClipboard(svgId) {
            const svgElement = document.querySelector(`.${svgId}`);
            if (!svgElement) {
                alert('SVG not found!');
                return;
            }
            
            const svgCode = svgElement.outerHTML;
            
            navigator.clipboard.writeText(svgCode)
                .then(() => {
                    alert('SVG code copied to clipboard!');
                })
                .catch(err => {
                    console.error('Failed to copy: ', err);
                    alert('Failed to copy SVG code');
                });
        }
    </script>
</body>
</html>
