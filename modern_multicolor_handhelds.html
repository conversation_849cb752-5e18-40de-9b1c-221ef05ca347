<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Multicolor Handheld Game Devices - Professional SVG Collection</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: white;
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 3rem;
        }

        .device-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .device-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            text-align: center;
        }

        .svg-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .copy-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            font-family: 'Inter', sans-serif;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .copy-btn.copied {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .footer p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Modern Multicolor Handheld Game Devices</h1>
        <div class="grid">
            <!-- Device 1: Aurora Gaming Pro -->
            <div class="device-card">
                <h3 class="device-title">Aurora Gaming Pro</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="aurora1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea"/>
                                <stop offset="50%" style="stop-color:#764ba2"/>
                                <stop offset="100%" style="stop-color:#f093fb"/>
                            </linearGradient>
                            <linearGradient id="screen1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#000"/>
                                <stop offset="100%" style="stop-color:#1a1a1a"/>
                            </linearGradient>
                        </defs>
                        <rect x="30" y="30" width="240" height="140" rx="20" fill="url(#aurora1)" stroke="#4c51bf" stroke-width="2"/>
                        <rect x="80" y="50" width="140" height="80" rx="8" fill="url(#screen1)" stroke="#2d3748" stroke-width="1"/>
                        <circle cx="60" cy="150" r="15" fill="#ff6b6b" stroke="#e53e3e" stroke-width="2"/>
                        <circle cx="240" cy="150" r="15" fill="#4ecdc4" stroke="#319795" stroke-width="2"/>
                        <rect x="40" y="140" width="8" height="20" rx="4" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <rect x="252" y="140" width="8" height="20" rx="4" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <rect x="130" y="140" width="40" height="12" rx="6" fill="#68d391" stroke="#38a169" stroke-width="1"/>
                        <circle cx="200" cy="70" r="3" fill="#ff6b6b"/>
                        <circle cx="210" cy="70" r="3" fill="#4ecdc4"/>
                        <circle cx="220" cy="70" r="3" fill="#ffd93d"/>
                        <rect x="85" y="25" width="130" height="4" rx="2" fill="#a78bfa" opacity="0.8"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 0)">Copy SVG Code</button>
            </div>

            <!-- Device 2: Neon Strike -->
            <div class="device-card">
                <h3 class="device-title">Neon Strike</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="neon2" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1a1a1a"/>
                                <stop offset="100%" style="stop-color:#2d3748"/>
                            </linearGradient>
                        </defs>
                        <rect x="25" y="25" width="250" height="150" rx="25" fill="url(#neon2)" stroke="#4a5568" stroke-width="2"/>
                        <rect x="75" y="45" width="150" height="90" rx="10" fill="#000" stroke="#ff00ff" stroke-width="2"/>
                        <circle cx="55" cy="155" r="12" fill="#ff00ff" stroke="#e91e63" stroke-width="2"/>
                        <circle cx="245" cy="155" r="12" fill="#00ffff" stroke="#00bcd4" stroke-width="2"/>
                        <rect x="35" y="145" width="6" height="15" rx="3" fill="#ffff00" stroke="#ffc107" stroke-width="1"/>
                        <rect x="259" y="145" width="6" height="15" rx="3" fill="#ffff00" stroke="#ffc107" stroke-width="1"/>
                        <rect x="125" y="145" width="50" height="10" rx="5" fill="#ff6b6b" stroke="#f56565" stroke-width="1"/>
                        <rect x="80" y="20" width="140" height="3" rx="1.5" fill="#ff00ff" opacity="0.8"/>
                        <circle cx="200" cy="65" r="2" fill="#00ffff"/>
                        <circle cx="210" cy="65" r="2" fill="#ff00ff"/>
                        <circle cx="220" cy="65" r="2" fill="#ffff00"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 1)">Copy SVG Code</button>
            </div>

            <!-- Device 3: Prismatic Elite -->
            <div class="device-card">
                <h3 class="device-title">Prismatic Elite</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="prismatic3" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ff9a9e"/>
                                <stop offset="25%" style="stop-color:#fecfef"/>
                                <stop offset="50%" style="stop-color:#fecfef"/>
                                <stop offset="75%" style="stop-color:#a8edea"/>
                                <stop offset="100%" style="stop-color:#fed6e3"/>
                            </linearGradient>
                        </defs>
                        <rect x="35" y="35" width="230" height="130" rx="18" fill="url(#prismatic3)" stroke="#e53e3e" stroke-width="2"/>
                        <rect x="85" y="55" width="130" height="75" rx="8" fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
                        <circle cx="65" cy="145" r="10" fill="#4299e1" stroke="#3182ce" stroke-width="2"/>
                        <circle cx="235" cy="145" r="10" fill="#48bb78" stroke="#38a169" stroke-width="2"/>
                        <rect x="45" y="135" width="5" height="12" rx="2.5" fill="#ed8936" stroke="#dd6b20" stroke-width="1"/>
                        <rect x="250" y="135" width="5" height="12" rx="2.5" fill="#ed8936" stroke="#dd6b20" stroke-width="1"/>
                        <rect x="135" y="140" width="30" height="8" rx="4" fill="#9f7aea" stroke="#805ad5" stroke-width="1"/>
                        <circle cx="190" cy="75" r="2.5" fill="#f56565"/>
                        <circle cx="200" cy="75" r="2.5" fill="#4299e1"/>
                        <circle cx="210" cy="75" r="2.5" fill="#48bb78"/>
                        <circle cx="220" cy="75" r="2.5" fill="#ed8936"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 2)">Copy SVG Code</button>
            </div>

            <!-- Device 4: Cyber Nexus -->
            <div class="device-card">
                <h3 class="device-title">Cyber Nexus</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="cyber4" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#0f2027"/>
                                <stop offset="50%" style="stop-color:#203a43"/>
                                <stop offset="100%" style="stop-color:#2c5364"/>
                            </linearGradient>
                        </defs>
                        <rect x="40" y="40" width="220" height="120" rx="15" fill="url(#cyber4)" stroke="#00d4ff" stroke-width="2"/>
                        <rect x="90" y="60" width="120" height="70" rx="6" fill="#000" stroke="#00d4ff" stroke-width="1"/>
                        <circle cx="70" cy="140" r="8" fill="#00d4ff" stroke="#0891b2" stroke-width="2"/>
                        <circle cx="230" cy="140" r="8" fill="#ff0080" stroke="#be185d" stroke-width="2"/>
                        <rect x="50" y="130" width="4" height="10" rx="2" fill="#00ff88" stroke="#059669" stroke-width="1"/>
                        <rect x="246" y="130" width="4" height="10" rx="2" fill="#00ff88" stroke="#059669" stroke-width="1"/>
                        <rect x="140" y="135" width="20" height="6" rx="3" fill="#ff4081" stroke="#e91e63" stroke-width="1"/>
                        <rect x="95" y="35" width="110" height="2" rx="1" fill="#00d4ff" opacity="0.8"/>
                        <circle cx="180" cy="80" r="2" fill="#00d4ff"/>
                        <circle cx="190" cy="80" r="2" fill="#ff0080"/>
                        <circle cx="200" cy="80" r="2" fill="#00ff88"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 3)">Copy SVG Code</button>
            </div>

            <!-- Device 5: Quantum Wave -->
            <div class="device-card">
                <h3 class="device-title">Quantum Wave</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="quantum5" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#8360c3"/>
                                <stop offset="50%" style="stop-color:#2ebf91"/>
                                <stop offset="100%" style="stop-color:#f093fb"/>
                            </linearGradient>
                        </defs>
                        <rect x="30" y="30" width="240" height="140" rx="22" fill="url(#quantum5)" stroke="#553c9a" stroke-width="2"/>
                        <rect x="80" y="50" width="140" height="85" rx="10" fill="#1a1a1a" stroke="#4a5568" stroke-width="1"/>
                        <circle cx="60" cy="150" r="12" fill="#ffd93d" stroke="#d69e2e" stroke-width="2"/>
                        <circle cx="240" cy="150" r="12" fill="#ff6b6b" stroke="#e53e3e" stroke-width="2"/>
                        <rect x="40" y="140" width="6" height="15" rx="3" fill="#4ecdc4" stroke="#319795" stroke-width="1"/>
                        <rect x="254" y="140" width="6" height="15" rx="3" fill="#4ecdc4" stroke="#319795" stroke-width="1"/>
                        <rect x="130" y="145" width="40" height="10" rx="5" fill="#a78bfa" stroke="#805ad5" stroke-width="1"/>
                        <circle cx="190" cy="70" r="3" fill="#ffd93d"/>
                        <circle cx="200" cy="70" r="3" fill="#ff6b6b"/>
                        <circle cx="210" cy="70" r="3" fill="#4ecdc4"/>
                        <rect x="85" y="25" width="130" height="3" rx="1.5" fill="#f093fb" opacity="0.9"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 4)">Copy SVG Code</button>
            </div>

            <!-- Device 6: Holographic Pro -->
            <div class="device-card">
                <h3 class="device-title">Holographic Pro</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="holo6" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#ff9a56"/>
                                <stop offset="50%" style="stop-color:#ff6b9d"/>
                                <stop offset="100%" style="stop-color:#c44569"/>
                            </linearGradient>
                        </defs>
                        <rect x="35" y="35" width="230" height="130" rx="20" fill="url(#holo6)" stroke="#c44569" stroke-width="2"/>
                        <rect x="85" y="55" width="130" height="75" rx="8" fill="#2d3748" stroke="#4a5568" stroke-width="1"/>
                        <circle cx="65" cy="145" r="11" fill="#00d4ff" stroke="#0891b2" stroke-width="2"/>
                        <circle cx="235" cy="145" r="11" fill="#ff4081" stroke="#e91e63" stroke-width="2"/>
                        <rect x="45" y="135" width="5" height="12" rx="2.5" fill="#00ff88" stroke="#059669" stroke-width="1"/>
                        <rect x="250" y="135" width="5" height="12" rx="2.5" fill="#00ff88" stroke="#059669" stroke-width="1"/>
                        <rect x="135" y="140" width="30" height="8" rx="4" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <circle cx="185" cy="75" r="2.5" fill="#00d4ff"/>
                        <circle cx="195" cy="75" r="2.5" fill="#ff4081"/>
                        <circle cx="205" cy="75" r="2.5" fill="#00ff88"/>
                        <circle cx="215" cy="75" r="2.5" fill="#ffd93d"/>
                        <rect x="90" y="30" width="120" height="2" rx="1" fill="#ff6b9d" opacity="0.8"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 5)">Copy SVG Code</button>
            </div>

            <!-- Device 7: Spectrum Master -->
            <div class="device-card">
                <h3 class="device-title">Spectrum Master</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="spectrum7" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea"/>
                                <stop offset="25%" style="stop-color:#764ba2"/>
                                <stop offset="50%" style="stop-color:#f093fb"/>
                                <stop offset="75%" style="stop-color:#f5576c"/>
                                <stop offset="100%" style="stop-color:#4facfe"/>
                            </linearGradient>
                        </defs>
                        <rect x="25" y="25" width="250" height="150" rx="25" fill="url(#spectrum7)" stroke="#4c51bf" stroke-width="2"/>
                        <rect x="75" y="45" width="150" height="90" rx="12" fill="#000" stroke="#2d3748" stroke-width="1"/>
                        <circle cx="55" cy="155" r="13" fill="#ff6b6b" stroke="#e53e3e" stroke-width="2"/>
                        <circle cx="245" cy="155" r="13" fill="#4ecdc4" stroke="#319795" stroke-width="2"/>
                        <rect x="35" y="145" width="7" height="16" rx="3.5" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <rect x="258" y="145" width="7" height="16" rx="3.5" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <rect x="125" y="150" width="50" height="12" rx="6" fill="#68d391" stroke="#38a169" stroke-width="1"/>
                        <circle cx="195" cy="65" r="3" fill="#ff6b6b"/>
                        <circle cx="205" cy="65" r="3" fill="#4ecdc4"/>
                        <circle cx="215" cy="65" r="3" fill="#ffd93d"/>
                        <circle cx="225" cy="65" r="3" fill="#68d391"/>
                        <rect x="80" y="20" width="140" height="3" rx="1.5" fill="#f093fb" opacity="0.9"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 6)">Copy SVG Code</button>
            </div>

            <!-- Device 8: Cosmic Gamer -->
            <div class="device-card">
                <h3 class="device-title">Cosmic Gamer</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <radialGradient id="cosmic8" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#1a1a2e"/>
                                <stop offset="50%" style="stop-color:#16213e"/>
                                <stop offset="100%" style="stop-color:#0f3460"/>
                            </radialGradient>
                        </defs>
                        <rect x="40" y="40" width="220" height="120" rx="18" fill="url(#cosmic8)" stroke="#00d4ff" stroke-width="2"/>
                        <rect x="90" y="60" width="120" height="70" rx="8" fill="#000" stroke="#00d4ff" stroke-width="1"/>
                        <circle cx="70" cy="140" r="9" fill="#ff0080" stroke="#be185d" stroke-width="2"/>
                        <circle cx="230" cy="140" r="9" fill="#00ff88" stroke="#059669" stroke-width="2"/>
                        <rect x="50" y="130" width="4" height="12" rx="2" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <rect x="246" y="130" width="4" height="12" rx="2" fill="#ffd93d" stroke="#d69e2e" stroke-width="1"/>
                        <rect x="140" y="135" width="20" height="7" rx="3.5" fill="#a78bfa" stroke="#805ad5" stroke-width="1"/>
                        <circle cx="175" cy="80" r="2" fill="#ff0080"/>
                        <circle cx="185" cy="80" r="2" fill="#00ff88"/>
                        <circle cx="195" cy="80" r="2" fill="#ffd93d"/>
                        <circle cx="205" cy="80" r="2" fill="#00d4ff"/>
                        <rect x="95" y="35" width="110" height="2" rx="1" fill="#00d4ff" opacity="0.8"/>
                        <circle cx="120" cy="45" r="1" fill="#fff" opacity="0.8"/>
                        <circle cx="130" cy="48" r="0.5" fill="#fff" opacity="0.6"/>
                        <circle cx="170" cy="46" r="1" fill="#fff" opacity="0.7"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 7)">Copy SVG Code</button>
            </div>

            <!-- Device 9: Rainbow Elite -->
            <div class="device-card">
                <h3 class="device-title">Rainbow Elite</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="rainbow9" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#ff0000"/>
                                <stop offset="16.66%" style="stop-color:#ff8000"/>
                                <stop offset="33.33%" style="stop-color:#ffff00"/>
                                <stop offset="50%" style="stop-color:#00ff00"/>
                                <stop offset="66.66%" style="stop-color:#0080ff"/>
                                <stop offset="83.33%" style="stop-color:#8000ff"/>
                                <stop offset="100%" style="stop-color:#ff0080"/>
                            </linearGradient>
                        </defs>
                        <rect x="30" y="30" width="240" height="140" rx="20" fill="#2d3748" stroke="url(#rainbow9)" stroke-width="3"/>
                        <rect x="80" y="50" width="140" height="80" rx="8" fill="#000" stroke="#4a5568" stroke-width="1"/>
                        <circle cx="60" cy="150" r="12" fill="#ff0000" stroke="#cc0000" stroke-width="2"/>
                        <circle cx="240" cy="150" r="12" fill="#00ff00" stroke="#00cc00" stroke-width="2"/>
                        <rect x="40" y="140" width="6" height="15" rx="3" fill="#0080ff" stroke="#0066cc" stroke-width="1"/>
                        <rect x="254" y="140" width="6" height="15" rx="3" fill="#0080ff" stroke="#0066cc" stroke-width="1"/>
                        <rect x="130" y="145" width="40" height="10" rx="5" fill="#ff8000" stroke="#cc6600" stroke-width="1"/>
                        <circle cx="190" cy="70" r="3" fill="#ff0000"/>
                        <circle cx="200" cy="70" r="3" fill="#ffff00"/>
                        <circle cx="210" cy="70" r="3" fill="#00ff00"/>
                        <circle cx="220" cy="70" r="3" fill="#0080ff"/>
                        <rect x="85" y="25" width="130" height="3" rx="1.5" fill="url(#rainbow9)" opacity="0.8"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 8)">Copy SVG Code</button>
            </div>

            <!-- Device 10: Gradient Pro -->
            <div class="device-card">
                <h3 class="device-title">Gradient Pro</h3>
                <div class="svg-container">
                    <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="gradient10" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea"/>
                                <stop offset="100%" style="stop-color:#764ba2"/>
                            </linearGradient>
                        </defs>
                        <rect x="35" y="35" width="230" height="130" rx="18" fill="url(#gradient10)" stroke="#553c9a" stroke-width="2"/>
                        <rect x="85" y="55" width="130" height="75" rx="8" fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
                        <circle cx="65" cy="145" r="10" fill="#f56565" stroke="#e53e3e" stroke-width="2"/>
                        <circle cx="235" cy="145" r="10" fill="#4299e1" stroke="#3182ce" stroke-width="2"/>
                        <rect x="45" y="135" width="5" height="12" rx="2.5" fill="#48bb78" stroke="#38a169" stroke-width="1"/>
                        <rect x="250" y="135" width="5" height="12" rx="2.5" fill="#48bb78" stroke="#38a169" stroke-width="1"/>
                        <rect x="135" y="140" width="30" height="8" rx="4" fill="#ed8936" stroke="#dd6b20" stroke-width="1"/>
                        <circle cx="185" cy="75" r="2.5" fill="#f56565"/>
                        <circle cx="195" cy="75" r="2.5" fill="#4299e1"/>
                        <circle cx="205" cy="75" r="2.5" fill="#48bb78"/>
                        <circle cx="215" cy="75" r="2.5" fill="#ed8936"/>
                        <rect x="90" y="30" width="120" height="2" rx="1" fill="#a78bfa" opacity="0.8"/>
                    </svg>
                </div>
                <button class="copy-btn" onclick="copySVG(this, 9)">Copy SVG Code</button>
            </div>
        </body>
</html>